#ifndef MIDLINE_OFFSET_EXAMPLE_H
#define MIDLINE_OFFSET_EXAMPLE_H

#include "zf_common_headfile.h"

//-------------------------------------------------------------------------------------------------------------------
//  函数声明
//-------------------------------------------------------------------------------------------------------------------

// 中线偏移量使用示例
void midline_offset_example(void);

// 基于中线偏移量的控制决策示例
void control_decision_example(void);

// 动态调整目标点索引的示例
void dynamic_target_point_example(void);

// 偏移量数据记录示例
void offset_data_logging_example(void);

#endif
