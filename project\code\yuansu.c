#include "zf_common_headfile.h"
//环岛变量
volatile int Island_State=0;     //环岛状态标志
volatile int Left_Island_Flag=0; //左右环岛标志
volatile int Right_Island_Flag=0;//左右环岛标志
volatile int Left_Up_Guai[2];    //四个拐点的坐标存储，[0]存y，第某行，{1}存x，第某列
volatile int Right_Up_Guai[2];   //四个拐点的坐标存储，[0]存y，第某行，{1}存x，第某列
int l_cir[3];
int r_cir[3];
int fflag=0;
int lft_raw=0;
int rgt_raw=0;
int is_long_straight=0;         //长直道标志，0表示不是长直道，1表示是长直道

void Island_Detect()
{
    static float k=0;//3和5状态的k
    static int island_state_5_down[2]={0};//状态5时即将离开环岛，左右边界边最低点，[0]存y，第某行，{1}存x，第某列
    static int island_state_3_up[2]={0};//状态3时即将进入环岛用，左右上面角点[0]存y，第某行，{1}存x，第某列
    static int left_down_guai[2]={0};//四个拐点的坐标存储，[0]存y，第某行，{1}存x，第某列
    static int right_down_guai[2]={0};//四个拐点的坐标存储，[0]存y，第某行，{1}存x，第某列
    int monotonicity_change_line[2];//单调性改变点坐标，[0]寸某行，[1]寸某列
    int monotonicity_change_left_flag=0;//不转折是0
    int monotonicity_change_right_flag=0;//不转折是0
    int continuity_change_right_flag=0; //连续是0
    int continuity_change_left_flag=0;  //连续是0
    static int island_timer_start = 0; // misdetection timer start
    
    // 在push执行过程中锁定Island_State，即当inTextMode为true时不执行状态变更
    if (inTextMode == true)
    {
        // push执行过程中不更新环岛状态，直接返回
        return;
    }

    // misdetection exit: reset if stuck in state 1 or 2 for >1500ms
    if ((Island_State == 1 || Island_State == 2) && (ti - island_timer_start > 500000000)) {
        Island_State = 0;
        Left_Island_Flag = 0;
        Right_Island_Flag = 0;
        // fflag = 1;
    }

    //以下是常规判断法
    // continuity_change_left_flag=Continuity_Change_Left(MT9V03X_H-5,10);//连续性判断
    // continuity_change_right_flag=Continuity_Change_Right(MT9V03X_H-5,10);
    // monotonicity_change_right_flag=Monotonicity_Change_Right(MT9V03X_H-1-5,10);
    // monotonicity_change_left_flag=Monotonicity_Change_Left(MT9V03X_H-1-5,10);
    continuity_change_left_flag=Continuity_Change_Left(MT9V03X_H-1,10);//连续性判断
    continuity_change_right_flag=Continuity_Change_Right(MT9V03X_H-1,10);
    monotonicity_change_right_flag=Monotonicity_Change_Right(MT9V03X_H-1-10,10);
    monotonicity_change_left_flag=Monotonicity_Change_Left(MT9V03X_H-1-10,10);
    if(gpio_get_level(B17) == 0){
    tft180_show_int(112,64,continuity_change_left_flag,4);
    tft180_show_int(112,80,continuity_change_right_flag,4);
    tft180_show_int(112,96,monotonicity_change_left_flag,4);
    tft180_show_int(112,112,monotonicity_change_right_flag,4);
    }
    // tft180_show_int(40,80,Search_Stop_Line,4);
    if(Cross_Flag==0&&Island_State==0)
    {
        continuity_change_left_flag=Continuity_Change_Left(MT9V03X_H-1-5,10);//连续性判断
        continuity_change_right_flag=Continuity_Change_Right(MT9V03X_H-1-5,10);
        r_cir[0]=Get_Right_K(62,88); //斜率判断
        r_cir[1]=Get_Right_K(40,62);
        r_cir[2]=Get_Right_K(18,40);
        l_cir[0]=Get_Left_K(62,88);
        l_cir[1]=Get_Left_K(40,62);
        l_cir[2]=Get_Left_K(18,40);
        
        // 判断是否为长直道
        if ((abs(l_cir[0] - l_cir[1]) <= 2) && 
            (abs(l_cir[1] - l_cir[2]) <= 2) && 
            (abs(l_cir[0] - l_cir[2]) <= 2) &&
            (abs(r_cir[0] - r_cir[1]) <= 2) &&
            (abs(r_cir[1] - r_cir[2]) <= 2) &&
            (abs(r_cir[0] - r_cir[2]) <= 2)) {
            is_long_straight = 1;  // 设置长直道标志
        } else {
            is_long_straight = 0;  // 不是长直道
        }
        
        if(Left_Island_Flag==0)//左环
        {
            if(
               (abs(r_cir[0] - r_cir[1]) <= 2) &&
               (abs(r_cir[1]-r_cir[2])<=2) &&
               (abs(r_cir[0] - r_cir[2]) <= 2) 
               &&
               monotonicity_change_right_flag==0&& //右边是单调的
               continuity_change_left_flag!=0&& //左边是不连续的
               continuity_change_right_flag==0&& //左环岛右边是连续的
               Left_Lost_Time>=10&& //左边丢线很多
               Left_Lost_Time<=76&& //也不能全丢了
               Right_Lost_Time<=35
               &&//右边丢线较少
               Search_Stop_Line>=MT9V03X_H*0.95&& //搜索截止行看到很远
               Boundry_Start_Left>=85&&Boundry_Start_Right>=76
               && //边界起始点靠下
               Both_Lost_Time<=15&&L_corner_flag == 1&&L_corner_col>=6
               &&inTextMode != true&&box_detected != 1
            )//双边丢线少
            {

                left_down_guai[0]=Find_Left_Down_Point(MT9V03X_H-1,20);//找左下角点
                // tft180_show_int(80,60,left_down_guai[0],4);
                if(left_down_guai[0]>=40)//条件1很松，在这里判断拐点，位置不对，则是误判，跳出
                {

                    Island_State=1;
                    Left_Island_Flag=1;
                    island_timer_start = ti; // record entry into state 1
                }
                else//误判，归零
                {
                    Island_State=0;
                    Left_Island_Flag=0;
                }
            }
            // else{
            //     fflag=0;
            // }

        }
        if (Right_Island_Flag == 0) // 右环
        {
            if ((abs(l_cir[0] - l_cir[1]) <= 2) &&
                (abs(l_cir[1] - l_cir[2]) <= 2) &&
                (abs(l_cir[0] - l_cir[2]) <= 2) &&
                monotonicity_change_left_flag == 0 &&  // 左边是单调的
                continuity_change_right_flag != 0 &&   // 右边是不连续的
                continuity_change_left_flag == 0 &&    // 右环岛左边是连续的
                Right_Lost_Time >= 10 &&               // 右边丢线很多
                Right_Lost_Time <= 76 &&               // 也不能全丢了
                Left_Lost_Time <= 35 &&                // 左边丢线较少
                Search_Stop_Line >= MT9V03X_H * 0.95 && // 搜索截止行看到很远
                Boundry_Start_Right >= 85 && Boundry_Start_Left >= 76 && // 边界起始点靠下
                Both_Lost_Time <= 15&&R_corner_flag == 1&&R_corner_col<=154
                &&inTextMode != true&&box_detected != 1)                  // 双边丢线少
            {
                // fflag = 1;
                right_down_guai[0] = Find_Right_Down_Point(MT9V03X_H - 1, 20); // 找右下角点
                // tft180_show_int(80, 80, right_down_guai[0], 4);
                if (right_down_guai[0] >= 40) // 条件1很松，在这里判断拐点，位置不对，则是误判，跳出
                {
                    Island_State = 1;
                    Right_Island_Flag = 1;
                    island_timer_start = ti; // record entry into state 1
                }
                else // 误判，归零
                {
                    Island_State = 0;
                    Right_Island_Flag = 0;
                }
            }
        }
    }
    if(Left_Island_Flag==1)//1状态下拐点还在，没丢线
    {
        if(Island_State==1)
        {
            // monotonicity_change_line[0]=Monotonicity_Change_Left(30,5);//寻找单调性改变点
            // monotonicity_change_line[1]=Left_Line[monotonicity_change_line[0]];
            // Left_Add_Line((int)(monotonicity_change_line[1]*0.15),MT9V03X_H-1,monotonicity_change_line[1],monotonicity_change_line[0]);
            if((Island_State==1)&&(L_corner_row>85))//下方当丢线时候进2
            {
                fflag = 1;
                lft_raw=eulerAngle_yaw;
                Island_State=2;
                island_timer_start = ti; // record entry into state 2
            }
        }
        else if(Island_State==2)//下方角点消失，2状态时下方应该是丢线，上面是弧线
        {
            // 计算角度差，考虑跨越 ±180° 的情况
            int angleDiffLt = eulerAngle_yaw - lft_raw;

            // 处理跨越边界情况
            if (angleDiffLt <= -180)
                angleDiffLt += 360;
            else if (angleDiffLt > 180)
                angleDiffLt -= 360;
            // fflag=1;
            // monotonicity_change_line[0]=Monotonicity_Change_Left(60,10);//寻找单调性改变点
            // monotonicity_change_line[1]=Left_Line[monotonicity_change_line[0]];
            // Right_Add_Line((int)(monotonicity_change_line[1]*0.1+144),MT9V03X_H-1,monotonicity_change_line[1]*2.1-176,monotonicity_change_line[0]*0.8);
vx=0;
vy=72;
g_target_angular_velocity_setpoint=25;
            if((Island_State==2 &&monotonicity_change_left_flag != 0 &&
            monotonicity_change_right_flag != 0 &&
            continuity_change_right_flag != 0 &&
            continuity_change_left_flag != 0 && angleDiffLt >= 60) || 
            (Island_State==2 && angleDiffLt >= 80) ||
            (Island_State==2 && box_detected == 1))//当圆弧靠下时候，进3，或当角度大于80度时强制进入，或检测到箱子时强制进入
            {
                Island_State=3;//最长白列寻找范围也要改，见camera.c
                Left_Island_Flag=1;

            }
        }
        else if(Island_State==3)//连线，出环最后一步
        {
            // 计算角度差，考虑跨越 ±180° 的情况
            int angleDiffLt = lft_raw - eulerAngle_yaw;
        
            // 处理跨越边界情况
            if (angleDiffLt <= -180)
                angleDiffLt += 360;
            else if (angleDiffLt > 180)
                angleDiffLt -= 360;
        
            if((L_corner_flag == 1 && L_corner_row>=90 && L_corner_col<=40 && angleDiffLt >= 110&&inTextMode != true&&box_detected != 1) || 
              (angleDiffLt >= 120))  // 添加角度大于80度的强制退出条件
            {
                Island_State=4;
                // fflag=1;
            }
        }
        else if(Island_State==4) // 出环
        {
            // 计算角度差，考虑跨越 ±180° 的情况
            int angleDiffLt = eulerAngle_yaw - lft_raw;
        
            // 处理跨越边界情况
            if (angleDiffLt <= -180)
                angleDiffLt += 360;
            else if (angleDiffLt > 180)
                angleDiffLt -= 360;
        
            // Right_Add_Line(140,119,80,10);
            // Left_Add_Line(40,119,10,50); // 在左边界添加退出引导线
        vx=0;
            vy=82;
            g_target_angular_velocity_setpoint=30;
            // 当角度偏差在10度以内时，退出环岛状态
            if (abs(angleDiffLt) <= 10)
            {
                Island_State = 0;
                Left_Island_Flag = 0;
            }
        }

    }
if(Right_Island_Flag==1)
{
    if(Island_State==1)
    {
        if(R_corner_row>85) // 当右边界下方丢线时，进入状态2
        {
            fflag = 1;
            rgt_raw=eulerAngle_yaw;
            Island_State=2;
            island_timer_start = ti; // record entry into state 2
        }
    }
    else if(Island_State==2) // 下方角点消失，开始处理右边的弧线
    {
        // 计算角度差，考虑跨越 ±180° 的情况
        int angleDiffRt = eulerAngle_yaw - rgt_raw;

        // 处理跨越边界情况
        if (angleDiffRt <= -180)
            angleDiffRt += 360;
        else if (angleDiffRt > 180)
            angleDiffRt -= 360;

        // monotonicity_change_line[0]=Monotonicity_Change_Right(60,10); // 寻找右边单调性改变点
        // monotonicity_change_line[1]=Right_Line[monotonicity_change_line[0]];
        // Left_Add_Line((int)(monotonicity_change_line[1]*0.1),MT9V03X_H-1,monotonicity_change_line[1]*2.1,monotonicity_change_line[0]*0.8); // 在左边界添加引导线
vx=0;
vy=72;
g_target_angular_velocity_setpoint=-25;
        if((monotonicity_change_left_flag != 0 &&
           monotonicity_change_right_flag != 0 &&
           continuity_change_right_flag != 0 &&
           continuity_change_left_flag != 0 && angleDiffRt <= -60) ||
           (Island_State==2 && angleDiffRt <= -80) ||
           (Island_State==2 && box_detected == 1))//当满足原条件，或角度小于-80度，或检测到箱子时强制进入
        {
            Island_State=3;
            Right_Island_Flag=1;
        }
    }
    else if(Island_State==3) // 准备退出环岛
    {
        // 计算角度差，考虑跨越 ±180° 的情况
        int angleDiffRt = rgt_raw - eulerAngle_yaw;
        
        // 处理跨越边界情况
        if (angleDiffRt <= -180)
            angleDiffRt += 360;
        else if (angleDiffRt > 180)
            angleDiffRt -= 360;

        if((R_corner_flag == 1 && R_corner_row >= 90 && R_corner_col >= 80 && angleDiffRt <= -110&&inTextMode != true&&box_detected != 1)||
        (angleDiffRt <= -120)) 
        {
            Island_State=4;
        }
    }

            else if(Island_State==4)//出环
        {
            // 计算角度差，考虑跨越 ±180° 的情况
            int angleDiffRt = eulerAngle_yaw - rgt_raw;
            
            // 处理跨越边界情况
            if (angleDiffRt <= -180)
                angleDiffRt += 360;
            else if (angleDiffRt > 180)
                angleDiffRt -= 360;
            
            // Left_Add_Line(20,119,80,10); // 在左边界添加退出引导线
            // Right_Add_Line(120,119,150,50);
                    vx=0;
            vy=82;
            g_target_angular_velocity_setpoint=-30;
            // 当角度偏差在10度以内时，退出环岛状态
            if (abs(angleDiffRt) <= 25)
            {
                Island_State = 0;
                Right_Island_Flag = 0;
                // 移除原有的计时器退出逻辑
            }
        }
}

}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     十字检测
  @param     null
  @return    null
  Sample     Cross_Detect(void);
  @note      利用四个拐点判别函数，查找四个角点，根据找到拐点的个数决定是否补线
-------------------------------------------------------------------------------------------------------------------*/
void Cross_Detect()
{
    int down_search_start=0;//下点搜索开始行
    Cross_Flag=0;
    if(Island_State==0)//与环岛互斥开
    {
        Left_Up_Find=0;
        Right_Up_Find=0;
        if(Both_Lost_Time>=10)//十字必定有双边丢线，在有双边丢线的情况下再开始找角点
        {
            Find_Up_Point( MT9V03X_H-1, 0 );
            if(Left_Up_Find==0&&Right_Up_Find==0)//只要没有同时找到两个上点，直接结束
            {
                return;
            }
        }
        if(Left_Up_Find!=0&&Right_Up_Find!=0)//找到两个上点，就找到十字了
        {
            fflag = 1;
            Cross_Flag=1;//对应标志位，便于各元素互斥掉
            down_search_start=Left_Up_Find>Right_Up_Find?Left_Up_Find:Right_Up_Find;//用两个上拐点坐标靠下者作为下点的搜索上限
            Find_Down_Point(MT9V03X_H-5,down_search_start+2);//在上拐点下2行作为下点的截止行
            if(Left_Down_Find<=Left_Up_Find)
            {
                Left_Down_Find=0;//下点不可能比上点还靠上
            }
            if(Right_Down_Find<=Right_Up_Find)
            {
                Right_Down_Find=0;//下点不可能比上点还靠上
            }
            if(Left_Down_Find!=0&&Right_Down_Find!=0)
            {//四个点都在，无脑连线，这种情况显然很少
                Left_Add_Line (Left_Line [Left_Up_Find ],Left_Up_Find ,Left_Line [Left_Down_Find ] ,Left_Down_Find);
                Right_Add_Line(Right_Line[Right_Up_Find],Right_Up_Find,Right_Line[Right_Down_Find],Right_Down_Find);
            }
            else if(Left_Down_Find==0&&Right_Down_Find!=0)//11//这里使用的都是斜率补线
            {//三个点                                     //01
                Lengthen_Left_Boundry(Left_Up_Find-1,MT9V03X_H-1);
                Right_Add_Line(Right_Line[Right_Up_Find],Right_Up_Find,Right_Line[Right_Down_Find],Right_Down_Find);
            }
            else if(Left_Down_Find!=0&&Right_Down_Find==0)//11
            {//三个点                                     //10
                Left_Add_Line (Left_Line [Left_Up_Find ],Left_Up_Find ,Left_Line [Left_Down_Find ] ,Left_Down_Find);
                Lengthen_Right_Boundry(Right_Up_Find-1,MT9V03X_H-1);
            }
            else if(Left_Down_Find==0&&Right_Down_Find==0)//11
            {//就俩上点                                   //00
                Lengthen_Left_Boundry (Left_Up_Find-1,MT9V03X_H-1);
                Lengthen_Right_Boundry(Right_Up_Find-1,MT9V03X_H-1);
            }
        }
        else
        {
            Cross_Flag=0;
        }
    }
// //角点相关变量，debug使用
//    tft180_show_uint(0,16*1,Cross_Flag,2);
//    tft180_show_uint(0,16*2,Island_State,2);
//    tft180_show_uint(0,16*3,Left_Up_Find,2);
//    tft180_show_uint(0,16*4,Right_Up_Find,2);
//    tft180_show_uint(0,16*5,Left_Down_Find,2);
//    tft180_show_uint(0,16*6,Right_Down_Find,2);
}
/*-------------------------------------------------------------------------------------------------------------------
  @brief     左赛道连续性检测
  @param     起始点，终止点
  @return    连续返回0，不连续返回断线出行数
  Sample     Continuity_Change_Left(int start,int end);
  @note      连续性的阈值设置为5，可更改
-------------------------------------------------------------------------------------------------------------------*/
int Continuity_Change_Left(int start,int end)//连续性阈值设置为5
{
    int i;
    int t;
    int continuity_change_flag=0;
    if(Left_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没必要判断了
       return 1;
    if(Search_Stop_Line<=5)//搜索截止行很矮
       return 1;
    if(start>=MT9V03X_H-1-5)//数组越界保护
        start=MT9V03X_H-1-5;
    if(end<=5)
       end=5;
    if(start<end)//都是从下往上计算的，反了就互换一下
    {
       t=start;
       start=end;
       end=t;
    }

    for(i=start;i>=end;i--)
    {
       if(abs(Left_Line[i]-Left_Line[i-1])>=5)//连续判断阈值是5,可更改
       {
            continuity_change_flag=i;
            break;
       }
    }
    return continuity_change_flag;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     右赛道连续性检测
  @param     起始点，终止点
  @return    连续返回0，不连续返回断线处行数
  Sample     continuity_change_flag=Continuity_Change_Right(int start,int end)
  @note      连续性的阈值设置为5，可更改
-------------------------------------------------------------------------------------------------------------------*/
int Continuity_Change_Right(int start,int end)
{
    int i;
    int t;
    int continuity_change_flag=0;
    if(Right_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没必要判断了
       return 1;
    if(start>=MT9V03X_H-5)//数组越界保护
        start=MT9V03X_H-5;
    if(end<=5)
       end=5;
    if(start<end)//都是从下往上计算的，反了就互换一下
    {
       t=start;
       start=end;
       end=t;
    }

    for(i=start;i>=end;i--)
    {
        if(abs(Right_Line[i]-Right_Line[i-1])>=5)//连续性阈值是5，可更改
       {
            continuity_change_flag=i;
            break;
       }
    }
    return continuity_change_flag;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     左下角点检测
  @param     起始点，终止点
  @return    返回角点所在的行数，找不到返回0
  Sample     Find_Left_Down_Point(int start,int end);
  @note      角点检测阈值可根据实际值更改
-------------------------------------------------------------------------------------------------------------------*/
int Find_Left_Down_Point(int start,int end)//找四个角点，返回值是角点所在的行数
{
    int i,t;
    int left_down_line=0;
    if(Left_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没有拐点判断的意义
       return left_down_line;
    if(start<end)
    {
        t=start;
        start=end;
        end=t;
    }
    if(start>=MT9V03X_H-1-5)//下面5行数据不稳定，不能作为边界点来判断，舍弃
        start=MT9V03X_H-1-5;
    if(end<=MT9V03X_H-Search_Stop_Line)
        end=MT9V03X_H-Search_Stop_Line;
    if(end<=5)
       end=5;
    for(i=start;i>=end;i--)
    {
        if(left_down_line==0&&//只找第一个符合条件的点
           abs(Left_Line[i]-Left_Line[i+1])<=5&&//角点的阈值可以更改
           abs(Left_Line[i+1]-Left_Line[i+2])<=5&&
           abs(Left_Line[i+2]-Left_Line[i+3])<=5&&
              (Left_Line[i]-Left_Line[i-2])>=5&&
              (Left_Line[i]-Left_Line[i-3])>=10&&
              (Left_Line[i]-Left_Line[i-4])>=10)
        {
            left_down_line=i;//获取行数即可
            break;
        }
    }
    return left_down_line;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     左上角点检测
  @param     起始点，终止点
  @return    返回角点所在的行数，找不到返回0
  Sample     Find_Left_Up_Point(int start,int end);
  @note      角点检测阈值可根据实际值更改
-------------------------------------------------------------------------------------------------------------------*/
int Find_Left_Up_Point(int start,int end)//找四个角点，返回值是角点所在的行数
{
    int i,t;
    int left_up_line=0;
    if(Left_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没有拐点判断的意义
       return left_up_line;
    if(start<end)
    {
        t=start;
        start=end;
        end=t;
    }
    if(end<=MT9V03X_H-Search_Stop_Line)//搜索截止行往上的全都不判
        end=MT9V03X_H-Search_Stop_Line;
    if(end<=5)//即使最长白列非常长，也要舍弃部分点，防止数组越界
        end=5;
    if(start>=MT9V03X_H-1-5)
        start=MT9V03X_H-1-5;
    for(i=start;i>=end;i--)
    {
        if(left_up_line==0&&//只找第一个符合条件的点
           abs(Left_Line[i]-Left_Line[i-1])<=5&&
           abs(Left_Line[i-1]-Left_Line[i-2])<=5&&
           abs(Left_Line[i-2]-Left_Line[i-3])<=5&&
              (Left_Line[i]-Left_Line[i+2])>=8&&
              (Left_Line[i]-Left_Line[i+3])>=15&&
              (Left_Line[i]-Left_Line[i+4])>=15)
        {
            left_up_line=i;//获取行数即可
            break;
        }
    }
    return left_up_line;//如果是MT9V03X_H-1，说明没有这么个拐点
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     右下角点检测
  @param     起始点，终止点
  @return    返回角点所在的行数，找不到返回0
  Sample     Find_Right_Down_Point(int start,int end);
  @note      角点检测阈值可根据实际值更改
-------------------------------------------------------------------------------------------------------------------*/
int Find_Right_Down_Point(int start,int end)//找四个角点，返回值是角点所在的行数
{
    int i,t;
    int right_down_line=0;
    if(Right_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没有拐点判断的意义
        return right_down_line;
    if(start<end)
    {
        t=start;
        start=end;
        end=t;
    }
    if(start>=MT9V03X_H-1-5)//下面5行数据不稳定，不能作为边界点来判断，舍弃
        start=MT9V03X_H-1-5;
    if(end<=MT9V03X_H-Search_Stop_Line)
        end=MT9V03X_H-Search_Stop_Line;
    if(end<=5)
       end=5;
    for(i=start;i>=end;i--)
    {
        if(right_down_line==0&&//只找第一个符合条件的点
           abs(Right_Line[i]-Right_Line[i+1])<=5&&//角点的阈值可以更改
           abs(Right_Line[i+1]-Right_Line[i+2])<=5&&
           abs(Right_Line[i+2]-Right_Line[i+3])<=5&&
              (Right_Line[i]-Right_Line[i-2])<=-5&&
              (Right_Line[i]-Right_Line[i-3])<=-10&&
              (Right_Line[i]-Right_Line[i-4])<=-10)
        {
            right_down_line=i;//获取行数即可
            break;
        }
    }
    return right_down_line;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     右上角点检测
  @param     起始点，终止点
  @return    返回角点所在的行数，找不到返回0
  Sample     Find_Right_Up_Point(int start,int end);
  @note      角点检测阈值可根据实际值更改
-------------------------------------------------------------------------------------------------------------------*/
int Find_Right_Up_Point(int start,int end)//找四个角点，返回值是角点所在的行数
{
    int i,t;
    int right_up_line=0;
    if(Right_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没有拐点判断的意义
        return right_up_line;
    if(start<end)
    {
        t=start;
        start=end;
        end=t;
    }
    if(end<=MT9V03X_H-Search_Stop_Line)//搜索截止行往上的全都不判
        end=MT9V03X_H-Search_Stop_Line;
    if(end<=5)//及时最长白列非常长，也要舍弃部分点，防止数组越界
        end=5;
    if(start>=MT9V03X_H-1-5)
        start=MT9V03X_H-1-5;
    for(i=start;i>=end;i--)
    {
        if(right_up_line==0&&//只找第一个符合条件的点
           abs(Right_Line[i]-Right_Line[i-1])<=5&&//下面两行位置差不多
           abs(Right_Line[i-1]-Right_Line[i-2])<=5&&
           abs(Right_Line[i-2]-Right_Line[i-3])<=5&&
              (Right_Line[i]-Right_Line[i+2])<=-8&&
              (Right_Line[i]-Right_Line[i+3])<=-15&&
              (Right_Line[i]-Right_Line[i+4])<=-15)
        {
            right_up_line=i;//获取行数即可
            break;
        }
    }
    return right_up_line;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     单调性突变检测
  @param     起始点，终止行
  @return    点所在的行数，找不到返回0
  Sample     Find_Right_Up_Point(int start,int end);
  @note      前5后5它最大（最小），那他就是角点
-------------------------------------------------------------------------------------------------------------------*/
int Monotonicity_Change_Left(int start,int end)//单调性改变，返回值是单调性改变点所在的行数
{
    int i;
    int monotonicity_change_line=0;
    if(Left_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没有单调性判断的意义
       return monotonicity_change_line;
    if(start>=MT9V03X_H-1-5)//数组越界保护，在判断第i个点时
       start=MT9V03X_H-1-5; //要访问它前后5个点，数组两头的点要不能作为起点终点
    if(end<=5)
        end=5;
    if(start<=end)//递减计算，入口反了，直接返回0
      return monotonicity_change_line;
    for(i=start;i>=end;i--)//会读取前5后5数据，所以前面对输入范围有要求
    {
        if(Left_Line[i]==Left_Line[i+5]&&Left_Line[i]==Left_Line[i-5]&&
        Left_Line[i]==Left_Line[i+4]&&Left_Line[i]==Left_Line[i-4]&&
        Left_Line[i]==Left_Line[i+3]&&Left_Line[i]==Left_Line[i-3]&&
        Left_Line[i]==Left_Line[i+2]&&Left_Line[i]==Left_Line[i-2]&&
        Left_Line[i]==Left_Line[i+1]&&Left_Line[i]==Left_Line[i-1])
        {//一堆数据一样，显然不能作为单调转折点
            continue;
        }
        else if(Left_Line[i]>=Left_Line[i+5]&&Left_Line[i]>=Left_Line[i-5]&&
        Left_Line[i]>=Left_Line[i+4]&&Left_Line[i]>=Left_Line[i-4]&&
        Left_Line[i]>=Left_Line[i+3]&&Left_Line[i]>=Left_Line[i-3]&&
        Left_Line[i]>=Left_Line[i+2]&&Left_Line[i]>=Left_Line[i-2]&&
        Left_Line[i]>=Left_Line[i+1]&&Left_Line[i]>=Left_Line[i-1])
        {//就很暴力，这个数据是在前5，后5中最大的（可以取等），那就是单调突变点
            monotonicity_change_line=i;
            break;
        }
    }
    return monotonicity_change_line;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     单调性突变检测
  @param     起始点，终止行
  @return    点所在的行数，找不到返回0
  Sample     Find_Right_Up_Point(int start,int end);
  @note      前5后5它最大（最小），那他就是角点
-------------------------------------------------------------------------------------------------------------------*/
int Monotonicity_Change_Right(int start,int end)//单调性改变，返回值是单调性改变点所在的行数
{
    int i;
    int monotonicity_change_line=0;

    if(Right_Lost_Time>=0.9*MT9V03X_H)//大部分都丢线，没有单调性判断的意义
        return monotonicity_change_line;
    if(start>=MT9V03X_H-1-5)//数组越界保护
        start=MT9V03X_H-1-5;
     if(end<=5)
        end=5;
    if(start<=end)
        return monotonicity_change_line;
    for(i=start;i>=end;i--)//会读取前5后5数据，所以前面对输入范围有要求
    {
        if(Right_Line[i]==Right_Line[i+5]&&Right_Line[i]==Right_Line[i-5]&&
        Right_Line[i]==Right_Line[i+4]&&Right_Line[i]==Right_Line[i-4]&&
        Right_Line[i]==Right_Line[i+3]&&Right_Line[i]==Right_Line[i-3]&&
        Right_Line[i]==Right_Line[i+2]&&Right_Line[i]==Right_Line[i-2]&&
        Right_Line[i]==Right_Line[i+1]&&Right_Line[i]==Right_Line[i-1])
        {//一堆数据一样，显然不能作为单调转折点
            continue;
        }
        else if(Right_Line[i]<=Right_Line[i+5]&&Right_Line[i]<=Right_Line[i-5]&&
        Right_Line[i]<=Right_Line[i+4]&&Right_Line[i]<=Right_Line[i-4]&&
        Right_Line[i]<=Right_Line[i+3]&&Right_Line[i]<=Right_Line[i-3]&&
        Right_Line[i]<=Right_Line[i+2]&&Right_Line[i]<=Right_Line[i-2]&&
        Right_Line[i]<=Right_Line[i+1]&&Right_Line[i]<=Right_Line[i-1])
        {//就很暴力，这个数据是在前5，后5中最大的，那就是单调突变点
            monotonicity_change_line=i;
            break;
        }
    }
    return monotonicity_change_line;
}


/*-------------------------------------------------------------------------------------------------------------------
  @brief     通过斜率，定点补线--
  @param     k       输入斜率
             startY  输入起始点纵坐标
             endY    结束点纵坐标
  @return    null
  Sample     K_Add_Boundry_Left(float k,int startX,int startY,int endY);
  @note      补得线直接贴在边线上
-------------------------------------------------------------------------------------------------------------------*/
void K_Add_Boundry_Left(float k,int startX,int startY,int endY)
{
    int i,t;
    if(startY>=MT9V03X_H-1)
        startY=MT9V03X_H-1;
    else if(startY<=0)
        startY=0;
    if(endY>=MT9V03X_H-1)
        endY=MT9V03X_H-1;
    else if(endY<=0)
        endY=0;
    if(startY<endY)//--操作，start需要大
    {
        t=startY;
        startY=endY;
        endY=t;
    }
    for(i=startY;i>=endY;i--)
    {
        Left_Line[i]=(int)((i-startY)/k+startX);//(y-y1)=k(x-x1)变形，x=(y-y1)/k+x1
        if(Left_Line[i]>=MT9V03X_W-1)
        {
            Left_Line[i]=MT9V03X_W-1;
        }
        else if(Left_Line[i]<=0)
        {
            Left_Line[i]=0;
        }
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     通过斜率，定点补线
  @param     k       输入斜率
             startY  输入起始点纵坐标
             endY    结束点纵坐标
  @return    null    直接补边线
  Sample     K_Add_Boundry_Right(float k,int startX,int startY,int endY);
  @note      补得线直接贴在边线上
-------------------------------------------------------------------------------------------------------------------*/
void K_Add_Boundry_Right(float k,int startX,int startY,int endY)
{
    int i,t;
    if(startY>=MT9V03X_H-1)
        startY=MT9V03X_H-1;
    else if(startY<=0)
        startY=0;
    if(endY>=MT9V03X_H-1)
        endY=MT9V03X_H-1;
    else if(endY<=0)
        endY=0;
    if(startY<endY)
    {
        t=startY;
        startY=endY;
        endY=t;
    }
    for(i=startY;i>=endY;i--)
    {
        Right_Line[i]=(int)((i-startY)/k+startX);//(y-y1)=k(x-x1)变形，x=(y-y1)/k+x1
        if(Right_Line[i]>=MT9V03X_W-1)
        {
            Right_Line[i]=MT9V03X_W-1;
        }
        else if(Right_Line[i]<=0)
        {
            Right_Line[i]=0;
        }
    }
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     根据斜率划线
  @param     输入斜率，定点，画一条黑线
  @return    null
  Sample     K_Draw_Line(k, 20,MT9V03X_H-1 ,0)
  @note      补的就是一条线，需要重新扫线
-------------------------------------------------------------------------------------------------------------------*/
void K_Draw_Line(float k, int startX, int startY,int endY)
{
    int endX=0;

    if(startX>=MT9V03X_W-1)//限幅处理
        startX=MT9V03X_W-1;
    else if(startX<=0)
        startX=0;
    if(startY>=MT9V03X_H-1)
        startY=MT9V03X_H-1;
    else if(startY<=0)
        startY=0;
    if(endY>=MT9V03X_H-1)
        endY=MT9V03X_H-1;
    else if(endY<=0)
        endY=0;
    endX=(int)((endY-startY)/k+startX);//(y-y1)=k(x-x1)变形，x=(y-y1)/k+x1
    Draw_Line(startX,startY,endX,endY);
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     获取左赛道边界斜率
  @param     int start_line,int end_line，起始行，中止行
  @return    两点之间的斜率
  Sample     k=Get_Left_K(68,69);
  @note      两点之间得出斜率，默认第一个参数小，第二个参数大
-------------------------------------------------------------------------------------------------------------------*/
float Get_Left_K(int start_line,int end_line)
{
    if(start_line>=MT9V03X_H-1)
        start_line=MT9V03X_H-1;
    else if(start_line<=0)
        start_line=0;
    if(end_line>=MT9V03X_H-1)
        end_line=MT9V03X_H-1;
    else if(end_line<=0)
        end_line=0;
    float k=0;
    int t=0;
    if(start_line>end_line)//++访问，坐标反了互换
    {
        t=start_line;
        start_line=end_line;
        end_line=t;
    }
    k=(float)(((float)Left_Line[start_line]-(float)Left_Line[end_line])/(end_line-start_line+1));
    return k;
}

/*-------------------------------------------------------------------------------------------------------------------
  @brief     获取右赛道边界斜率
  @param     int start_line,int end_line，起始行，中止行
  @return    两点之间的斜率
  Sample     k=Get_Right_K(68,69);
  @note      两点之间得出斜率，默认第一个参数小，第二个参数大
-------------------------------------------------------------------------------------------------------------------*/
float Get_Right_K(int start_line,int end_line)
{
    if(start_line>=MT9V03X_H-1)
        start_line=MT9V03X_H-1;
    else if(start_line<=0)
        start_line=0;
    if(end_line>=MT9V03X_H-1)
        end_line=MT9V03X_H-1;
    else if(end_line<=0)
        end_line=0;
    float k=0;
    int t=0;
    if(start_line>end_line)//++访问，坐标反了互换
    {
        t=start_line;
        start_line=end_line;
        end_line=t;
    }
    k=(float)(((float)Right_Line[start_line]-(float)Right_Line[end_line])/(end_line-start_line+1));
    return k;
}
