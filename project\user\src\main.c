/*********************************************************************************************************************
* RT1064DVL6A Opensourec Library 即（RT1064DVL6A 开源库）是一个基于官方 SDK 接口的第三方开源库
* Copyright (c) 2022 SEEKFREE 逐飞科技
* 
* 本文件是 RT1064DVL6A 开源库的一部分
* 
* RT1064DVL6A 开源库 是免费软件
* 您可以根据自由软件基金会发布的 GPL（GNU General Public License，即 GNU通用公共许可证）的条款
* 即 GPL 的第3版（即 GPL3.0）或（您选择的）任何后来的版本，重新发布和/或修改它
* 
* 本开源库的发布是希望它能发挥作用，但并未对其作任何的保证
* 甚至没有隐含的适销性或适合特定用途的保证
* 更多细节请参见 GPL
* 
* 您应该在收到本开源库的同时收到一份 GPL 的副本
* 如果没有，请参阅<https://www.gnu.org/licenses/>
* 
* 额外注明：
* 本开源库使用 GPL3.0 开源许可证协议 以上许可申明为译文版本
* 许可申明英文版在 libraries/doc 文件夹下的 GPL3_permission_statement.txt 文件中
* 许可证副本在 libraries 文件夹下 即该文件夹下的 LICENSE 文件
* 欢迎各位使用并传播本程序 但修改内容时必须保留逐飞科技的版权声明（即本声明）
* 
* 文件名称          main
* 公司名称          成都逐飞科技有限公司
* 版本信息          查看 libraries/doc 文件夹内 version 文件 版本说明
* 开发环境          IAR 8.32.4 or MDK 5.33
* 适用平台          RT1064DVL6A
* 店铺链接          https://seekfree.taobao.com/
* 
* 修改记录
* 日期              作者                备注
* 2022-09-21        SeekFree            first version
********************************************************************************************************************/

#include "zf_common_headfile.h"
#include <sxt.h>

// 打开新的工程或者工程移动了位置务必执行以下操作
// 第一步 关闭上面所有打开的文件
// 第二步 project->clean  等待下方进度条走完

// 本例程是开源库移植用空工程



int main(void)
{
    clock_init(SYSTEM_CLOCK_600M);  // 不可删除
    debug_init();                   // 调试端口初始化

    // 此处编写用户代码 例如外设初始化代码等
    Init();
    static int prev_special_mode = -1; // 用于存储上一个 special_mode 的值
    
    // 添加GPIO引脚状态跟踪变量
    static uint8_t prev_c18_level = 1; // 存储C18引脚上一次的状态
    static uint8_t prev_c19_level = 1; // 存储C19引脚上一次的状态
    static uint8_t prev_b16_level = 1; // 存储B16引脚上一次的状态
    static uint8_t prev_b17_level = 1; // 存储B17引脚上一次的状态
    
    // 此处编写用户代码 例如外设初始化代码等
    while(1)
    {
        otsu_erzhihua((uint8 *)mt9v03x_image, (uint8_t *)&mt9v03x_image_2[0][0]);
        yyy = gpio_get_level(C30);
        //left = gpio_get_level(C25);
        //right = gpio_get_level(C27);
        left = 1;
        right = 1;
        update_menu_animation();
        button_handler();

        // 当 special_mode 刚进入 5 时，执行清屏操作
        if (special_mode == 5 && prev_special_mode != 5) {
            tft180_clear();
        }
        prev_special_mode = special_mode; // 更新 prev_special_mode

        // 根据special_mode显示不同的内容
        switch(special_mode) {
            case 0:
                // 菜单模式 - 只在状态变更时刷新菜单
                break;
                
            case 1:
                tft180_show_gray_image(0, 0, mt9v03x_image[0], MT9V03X_W, MT9V03X_H, MT9V03X_W, MT9V03X_H, 0);

                break;
                
            case 2:
                tft180_show_gray_image(0, 0, mt9v03x_image_2[0], MT9V03X_W, MT9V03X_H, 160, 120, 0);

                // tft180_show_int(100, 20, vx, 6);
                // tft180_show_int(100, 40, vy, 6);
                // tft180_show_int(100, 60, g_target_angular_velocity_setpoint, 6);
                // tft180_show_int(100, 100, slope, 6);
                break;
                
            case 3:
                // 处理special_mode 3
                // uart_write_byte(UART_2, 0x87);
                // uart_write_byte(UART_4, 0x87);
                //uart_write_byte(UART_2, 0x87);
                //tft180_show_gray_image(0, 0, PerImg_ip[0][0], MT9V03X_W, MT9V03X_H, 160, 120, 0);
                // if (1) {
                //     uint8_t show[RESULT_ROW][RESULT_COL];
                //         for(int i=0;i<RESULT_ROW;i++)
                //         {
                //             for(int j=0;j<RESULT_COL;j++)
                //             {
                //                 show[i][j]=ImageUsed[i][j];
                //             }
                //         }
                //     tft180_show_gray_image(0,0,show[0],RESULT_COL,RESULT_ROW,RESULT_COL,RESULT_ROW,0);//
                //     //mt9v03x_finish_flag_dvp = 0;
                // }
                set_target_point_index(15);
                balinyu();
                // 先显示逆透视图像
                tft180_show_gray_image(0, 0, inverse_perspective_image[0], RESULT_COL, RESULT_ROW, RESULT_COL, RESULT_ROW, 0);
                // 然后绘制边线
                draw_new_edge_lines();
                
                // 显示调试信息
                // tft180_show_string(0, 0, "L_len:");
                // tft180_show_int(40, 0, left_lenth, 3);
                // tft180_show_string(80, 0, "R_len:");
                // tft180_show_int(120, 0, right_lenth, 3);
                // tft180_show_string(0, 16, "L_start:");
                // tft180_show_int(60, 16, left_line_raw[0][1], 3);
                // tft180_show_string(80, 16, "R_start:");
                // tft180_show_int(140, 16, right_line_raw[0][1], 3);
                
                // // 显示调试信息
                // tft180_show_string(0, 0, "L:");
                // tft180_show_int(16, 0, left_lenth, 3);
                // tft180_show_string(45, 0, "R:");
                // tft180_show_int(61, 0, right_lenth, 3);


                // tft180_show_gray_image(0, 0, mt9v03x_image_2[0], MT9V03X_W, MT9V03X_H, 160, 120, 0);

                break;
                
            case 4:
                //zongsaoxian(); // 调用新的控制函数

                break;
                
            case 5:
                {
                    // 定义静态变量用于分页和切换控制
                    static uint32_t page_switch_time = 0;
                    static uint8_t current_page = 0;
                    const uint8_t items_per_page = 12; // 每页最多显示12个条目(2列x6行)
                    const uint8_t total_pages = (classIndex + items_per_page - 1) / items_per_page; // 向上取整计算总页数
                    
                    // 如果有多页，每2秒切换一次页面
                    if (total_pages > 1) {
                        if (tim >= page_switch_time) {
                            current_page = (current_page + 1) % total_pages;
                            tft180_clear(); // 切换页面前清屏
                            page_switch_time = tim + 2000; // 下次切换时间为2秒后
                        }
                    } else {
                        current_page = 0; // 只有一页时固定为第0页
                    }
                    
                    // 计算当前页的起始和结束索引
                    int start_idx = current_page * items_per_page;
                    int end_idx = start_idx + items_per_page;
                    if (end_idx > classIndex) end_idx = classIndex;
                    
                    // 显示当前页的条目
                    for (int i = start_idx; i < end_idx; i++) {
                        int local_idx = i - start_idx; // 当前页内的索引
                        int col = local_idx / 6;       // 每6个词组换一列
                        int row = local_idx % 6;       // 当前列中的行号
                        uint16 x = col * 72;           // 每列x坐标增加72
                        uint16 y = row * 20;           // 每行y坐标增加20
                        
                        // 显示序号(从1开始)
                        tft180_show_int(x, y, i + 1, 2);
                        uint16 content_x = x + 20; // 增加了一些空间以便更好地显示内容
                        
                        if (classValues[i].type == 0) {
                            uint8 curClass = classValues[i].value;
                            ChineseString cs = classChinese[curClass];
                            const uint8 *chinese_ptr = &chinese[cs.startPair * 2][0];
                            tft180_show_chinese(content_x, y, 16, chinese_ptr, cs.count, RGB565_RED);
                        } else if (classValues[i].type == 1) {
                            uint8 number_to_display = classValues[i].value;
                            tft180_show_int(content_x, y, number_to_display, 3);
                        }
                    }
                    
                    // // 在底部显示页码信息
                    // if (total_pages > 1) {
                    //     char page_info[16];
                    //     sprintf(page_info, "Page %d/%d", current_page + 1, total_pages);
                    //     tft180_show_string(0, 120, page_info);
                    // }
                }
                break;
        }

        // 获取当前GPIO状态
        uint8_t current_c18_level = gpio_get_level(C18);
        uint8_t current_c19_level = gpio_get_level(C19);
        uint8_t current_b16_level = gpio_get_level(B16);
        uint8_t current_b17_level = gpio_get_level(B17);
        
        // 检测GPIO从0变为1的上升沿并执行相应操作
        if(current_c18_level == 1 && prev_c18_level == 0) {
            tft180_clear(); // 清屏
            if(special_mode == 0) {
                display_menu(); // 如果在菜单模式，刷新菜单
            }
        }
        
        if(current_c19_level == 1 && prev_c19_level == 0) {
            tft180_clear(); // 清屏
            if(special_mode == 0) {
                display_menu(); // 如果在菜单模式，刷新菜单
            }
        }
        
        if(current_b16_level == 1 && prev_b16_level == 0) {
            tft180_clear(); // 清屏
            if(special_mode == 0) {
                display_menu(); // 如果在菜单模式，刷新菜单
            }
        }
        
        if(current_b17_level == 1 && prev_b17_level == 0) {
            tft180_clear(); // 清屏
            if(special_mode == 0) {
                display_menu(); // 如果在菜单模式，刷新菜单
            }
        }
        
        // 当GPIO为低电平时显示相应信息
        if(current_c18_level == 0) {
            tft180_show_int(0, 0, box_detected, 6);
            tft180_show_int(0, 16, pia, 6);
            tft180_show_int(0, 32, distance_mm, 6);
            tft180_show_int(0, 48, top_detected, 6);
            tft180_show_int(0, 64, pia1, 6);
            tft180_show_int(0, 80, slope, 6);
            tft180_show_int(20, 0, left, 6);
            tft180_show_int(20, 16, right, 6);
            tft180_show_int(20, 32, yyy, 6);
            tft180_show_int(20, 48, flag_finish, 6);
            tft180_show_int(20, 64, number, 6);
            tft180_show_int(20, 80, inTextMode, 6);
            tft180_show_int(40, 16, angle_slope, 6);
            tft180_show_int(40, 32, recorded_slope, 6);
            tft180_show_int(40, 48, slope_raw, 6);
            tft180_show_int(40, 64, anglepia, 6);
        }
        
        if(current_c19_level == 0) {
            imu660ra_euler_show();
        }
        
        if(current_b16_level == 0) {

                if(special_mode != 4) {
                // zongsaoxian();
                // zong();
            }
tft180_show_int(112, 0, vx, 5);
    tft180_show_int(112, 16, vy, 5);
    tft180_show_int(112, 32, g_target_angular_velocity_setpoint, 5);
    tft180_show_int(112, 48, L_start_x, 5);
    tft180_show_int(112, 64, R_start_x, 5);
    tft180_show_int(112, 80, mid_x_offset, 5);
    tft180_show_int(112, 96, mid_y_offset, 5);
    tft180_show_int(112, 112, mid_angle_offset, 5);

        }
        
        if(current_b17_level == 0) {
                        if(special_mode != 4) {
                //zongsaoxian();
            }

    // tft180_show_int(40,0,continuity_change_left_flag,4);
    // tft180_show_int(40,20,continuity_change_right_flag,4);
    // tft180_show_int(40,40,monotonicity_change_left_flag,4);
    // tft180_show_int(40,60,monotonicity_change_right_flag,4);
    // tft180_show_int(0, 0, L_corner_row, 5);
    // tft180_show_int(0, 16, L_corner_col, 5);
    // tft180_show_int(0, 32, R_corner_row, 5);
    // tft180_show_int(0, 48, R_corner_col, 5);
    // tft180_show_int(0, 64, R_edge_count, 5);
    // tft180_show_int(0, 80, L_edge_count, 5);
    // tft180_show_int(0, 96, R_corner_angle, 5);
    // tft180_show_int(0, 112, L_corner_angle, 5);
    //         tft180_show_int(24,0,Left_Lost_Time,4);
    //         tft180_show_int(24,16,Right_Lost_Time,4);
    //         tft180_show_int(24,32,Both_Lost_Time,4);
    //         tft180_show_int(24,48,Boundry_Start_Left,4);
    //         tft180_show_int(24,64,Boundry_Start_Right,4);
    //         tft180_show_int(24,112, feedbackposition1, 5);
    //         tft180_show_int(48, 80, Island_State, 5);
        }
        
        // 更新GPIO状态记录
        prev_c18_level = current_c18_level;
        prev_c19_level = current_c19_level;
        prev_b16_level = current_b16_level;
        prev_b17_level = current_b17_level;
    static uint32_t turn_off_time = 0;  // 静态变量，记录关闭时间

if (fflag == 1)
{
  // gpio_set_level(C7, 1); 
  if (turn_off_time == 0)  // 定时尚未启动
  {
      turn_off_time = ti + 300;  // 设置 300ms 后的关闭时间
      gpio_set_level(C7, 1);     // 设置 GPIO 为高电平
  }
  else if (ti >= turn_off_time)  // 300ms 已到
  {
      gpio_set_level(C7, 0);     // 设置 GPIO 为低电平
      fflag = 0;                 // 重置 fflag
      turn_off_time = 0;         // 重置定时器状态
  }
  else  // 300ms 未到
  {
      gpio_set_level(C7, 1);     // 保持 GPIO 高电平
  }
}
else
{
  gpio_set_level(C7, 0);     // fflag 为 0 时，GPIO 置低
  turn_off_time = 0;         // 重置定时器状态
}
    }
}
      // otsu_erzhihua((uint8 *)mt9v03x_image, (uint8_t *)&mt9v03x_image_2[0][0]);
      // // tft180_draw_line(0, 0, 100, 100,RGB565_RED);      // 坐标 0,0 到 10,10 画一条红色的线
      // // xunji_Mid();
      // zong();

      // draw_eight_neighborhood_boundaries();
      // tft180_show_int(0, 0, L_corner_row, 5);
      // tft180_show_int(0, 20, L_corner_col, 5);
      // tft180_show_int(0, 40, R_corner_row, 5);
      // tft180_show_int(0, 60, R_corner_col, 5);
      // tft180_show_int(0, 80, R_edge_count, 5);
      // tft180_show_int(0, 100, L_edge_count, 5);
      // tft180_show_int(20, 40, guai_x, 5);
      // tft180_show_int(20, 60, guai_y, 5); 

            //   static uint32_t turn_off_time = 0;  // 静态变量，记录关闭时间

// if (fflag == 1)
// {
//   // gpio_set_level(C7, 1); 
//   if (turn_off_time == 0)  // 定时尚未启动
//   {
//       turn_off_time = ti + 300;  // 设置 300ms 后的关闭时间
//       gpio_set_level(C7, 1);     // 设置 GPIO 为高电平
//   }
//   else if (ti >= turn_off_time)  // 300ms 已到
//   {
//       gpio_set_level(C7, 0);     // 设置 GPIO 为低电平
//       fflag = 0;                 // 重置 fflag
//       turn_off_time = 0;         // 重置定时器状态
//   }
//   else  // 300ms 未到
//   {
//       gpio_set_level(C7, 1);     // 保持 GPIO 高电平
//   }
// }
// else
// {
//   gpio_set_level(C7, 0);     // fflag 为 0 时，GPIO 置低
//   turn_off_time = 0;         // 重置定时器状态
// }



