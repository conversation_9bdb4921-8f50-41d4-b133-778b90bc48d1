//-------------------------------------------------------------------------------------------------------------------
//  中线偏移量计算使用示例
//  功能：演示如何使用中线偏移量计算功能
//-------------------------------------------------------------------------------------------------------------------

#include "zf_common_headfile.h"
#include "sxt.h"

//-------------------------------------------------------------------------------------------------------------------
//  @brief      中线偏移量使用示例
//  @return     void
//  @note       在主循环中调用此函数来获取和使用中线偏移量
//-------------------------------------------------------------------------------------------------------------------
void midline_offset_example(void)
{
    // 1. 设置目标点索引（可以根据需要修改）
    set_target_point_index(5);  // 设置为第5个点，可以改为其他值
    
    // 2. 执行图像处理和中线计算
    balinyu();  // 这个函数内部会调用 calculate_midline_offset()
    
    // 3. 获取计算结果
    // mid_x_offset: x偏移量，正值表示目标点在屏幕中心右侧
    // mid_y_offset: y偏移量，正值表示目标点在屏幕中心上方  
    // mid_angle_offset: 角度偏移量（度），正值表示向右偏转
    
    // 4. 在屏幕上显示偏移量信息（可选）
    tft180_show_float(0, 0, mid_x_offset, 2, 2);      // 显示x偏移量
    tft180_show_float(0, 20, mid_y_offset, 2, 2);     // 显示y偏移量  
    tft180_show_float(0, 40, mid_angle_offset, 2, 2); // 显示角度偏移量
    tft180_show_int(0, 60, target_point_index, 2);    // 显示目标点索引
    tft180_show_int(0, 80, mid_lenth, 3);             // 显示中线点总数
    
    // 5. 绘制目标点和连接线（可选）
    draw_midline_target_point();
    
    // 6. 使用偏移量进行控制决策的示例
    control_decision_example();
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      基于中线偏移量的控制决策示例
//  @return     void
//  @note       展示如何根据偏移量进行车辆控制
//-------------------------------------------------------------------------------------------------------------------
void control_decision_example(void)
{
    // 定义控制参数
    float steering_kp = 1.0f;    // 转向比例系数
    float speed_base = 50.0f;    // 基础速度
    float speed_reduction = 0.5f; // 转弯时的减速系数
    
    // 计算转向控制量
    float steering_control = mid_angle_offset * steering_kp;
    
    // 限制转向控制量范围
    if (steering_control > 30.0f) steering_control = 30.0f;
    if (steering_control < -30.0f) steering_control = -30.0f;
    
    // 根据角度偏移调整速度
    float speed_control = speed_base;
    if (fabs(mid_angle_offset) > 10.0f) {
        speed_control = speed_base * (1.0f - speed_reduction * fabs(mid_angle_offset) / 90.0f);
    }
    
    // 显示控制量（可选）
    tft180_show_float(80, 0, steering_control, 2, 2);  // 显示转向控制量
    tft180_show_float(80, 20, speed_control, 2, 2);    // 显示速度控制量
    
    // 这里可以将控制量应用到实际的电机控制中
    // 例如：
    // motor_set_steering(steering_control);
    // motor_set_speed(speed_control);
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      动态调整目标点索引的示例
//  @return     void
//  @note       根据车速或其他条件动态调整预瞄距离
//-------------------------------------------------------------------------------------------------------------------
void dynamic_target_point_example(void)
{
    static int current_speed = 50;  // 假设的当前速度
    
    // 根据速度动态调整目标点索引
    if (current_speed > 80) {
        set_target_point_index(8);   // 高速时预瞄更远
    } else if (current_speed > 50) {
        set_target_point_index(6);   // 中速时预瞄中等距离
    } else {
        set_target_point_index(4);   // 低速时预瞄较近
    }
    
    // 根据弯道情况调整
    if (fabs(mid_angle_offset) > 20.0f) {
        // 在急弯时减少预瞄距离
        int current_index = target_point_index;
        if (current_index > 3) {
            set_target_point_index(current_index - 1);
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      偏移量数据记录示例
//  @return     void
//  @note       记录偏移量数据用于调试和分析
//-------------------------------------------------------------------------------------------------------------------
void offset_data_logging_example(void)
{
    static int log_counter = 0;
    static float offset_history[100][3];  // 记录最近100次的偏移量数据
    
    // 记录当前偏移量
    offset_history[log_counter][0] = mid_x_offset;
    offset_history[log_counter][1] = mid_y_offset;
    offset_history[log_counter][2] = mid_angle_offset;
    
    log_counter = (log_counter + 1) % 100;
    
    // 计算最近10次的平均偏移量（平滑处理）
    float avg_x = 0, avg_y = 0, avg_angle = 0;
    int start_idx = (log_counter - 10 + 100) % 100;
    
    for (int i = 0; i < 10; i++) {
        int idx = (start_idx + i) % 100;
        avg_x += offset_history[idx][0];
        avg_y += offset_history[idx][1];
        avg_angle += offset_history[idx][2];
    }
    
    avg_x /= 10.0f;
    avg_y /= 10.0f;
    avg_angle /= 10.0f;
    
    // 显示平均值
    tft180_show_float(0, 100, avg_x, 2, 2);
    tft180_show_float(0, 120, avg_y, 2, 2);
    tft180_show_float(0, 140, avg_angle, 2, 2);
}
