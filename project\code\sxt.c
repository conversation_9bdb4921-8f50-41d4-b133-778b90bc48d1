#include "zf_common_headfile.h"
#include "sxt.h"
#include "yuansu.h"
#include "math.h"
uint8_t *PerImg_ip[RESULT_ROW][RESULT_COL];
uint8_t inverse_perspective_image[RESULT_ROW][RESULT_COL]; // 逆透视后的连续图像

// 更新逆透视图像的函数，将 ImageUsed (即 *PerImg_ip) 复制到连续内存中
void update_inverse_perspective_image(void) {
    // 将指针数组中的像素值复制到连续内存的图像中
    for (int i = 0; i < RESULT_ROW; i++) {
        for (int j = 0; j < RESULT_COL; j++) {
            inverse_perspective_image[i][j] = ImageUsed[i][j];
        }
    }
}

void ImagePerspective_Init(void) {

    static uint8_t BlackColor = 0;
    double change_un_Mat[3][3] ={{1.083333,-0.748454,17.637035},{0.000000,0.244163,-6.705097},{0.000000,-0.008827,1.242415}};
    for (int i = 0; i < RESULT_COL ;i++) {
        for (int j = 0; j < RESULT_ROW ;j++) {
            int local_x = (int) ((change_un_Mat[0][0] * i
                    + change_un_Mat[0][1] * j + change_un_Mat[0][2])
                    / (change_un_Mat[2][0] * i + change_un_Mat[2][1] * j
                            + change_un_Mat[2][2]));
            int local_y = (int) ((change_un_Mat[1][0] * i
                    + change_un_Mat[1][1] * j + change_un_Mat[1][2])
                    / (change_un_Mat[2][0] * i + change_un_Mat[2][1] * j
                            + change_un_Mat[2][2]));
            if (local_x
                    >= 0&& local_y >= 0 && local_y < USED_ROW && local_x < USED_COL){
                PerImg_ip[j][i] = &PER_IMG[local_y][local_x];
            }
            else {
                PerImg_ip[j][i] = &BlackColor;          //&PER_IMG[0][0];
            }

        }
    }
}


/*完成摄像头初始化后，调用一次ImagePerspective_Init，此后，直接调用ImageUsed   即为透视结果*/

// 全局定义正向透视变换矩阵 hd（change_un_Mat 的逆矩阵）
float hd[3][3] = {
    {0.92308, 2.92692, 2.69229},
    {0.0,     5.08841, 27.46124},
    {0.0,     0.03615,  0.99999}
};

float Get_angle(float Ax, float Ay, float Bx, float By, float Cx, float Cy) 
{ 
    float BA = 0.00;      // 向量 BA 的模
    float BC = 0.00;      // 向量 BC 的模
    float SBA_BC = 0.00;  // 向量点乘的值
    float angle = 0.00;   // 角度（弧度）

    // 变换点 A
    float denom_A = hd[2][0] * Ax + hd[2][1] * Ay + hd[2][2];
    if (fabs(denom_A) < 1e-6) return 0.0;  // 避免除零
    float AX = (hd[0][0] * Ax + hd[0][1] * Ay + hd[0][2]) / denom_A;
    float AY = (hd[1][0] * Ax + hd[1][1] * Ay + hd[1][2]) / denom_A;

    // 变换点 B
    float denom_B = hd[2][0] * Bx + hd[2][1] * By + hd[2][2];
    if (fabs(denom_B) < 1e-6) return 0.0;
    float BX = (hd[0][0] * Bx + hd[0][1] * By + hd[0][2]) / denom_B;
    float BY = (hd[1][0] * Bx + hd[1][1] * By + hd[1][2]) / denom_B;

    // 变换点 C
    float denom_C = hd[2][0] * Cx + hd[2][1] * Cy + hd[2][2];
    if (fabs(denom_C) < 1e-6) return 0.0;
    float CX = (hd[0][0] * Cx + hd[0][1] * Cy + hd[0][2]) / denom_C;
    float CY = (hd[1][0] * Cx + hd[1][1] * Cy + hd[1][2]) / denom_C;

    // 计算向量 BA 和 BC 的模
    BA = sqrt((AX - BX) * (AX - BX) + (AY - BY) * (AY - BY));
    BC = sqrt((CX - BX) * (CX - BX) + (CY - BY) * (CY - BY));

    // 检查向量模是否为零
    if (BA < 1e-6 || BC < 1e-6) return 0.0;

    // 计算点积
    SBA_BC = (AX - BX) * (CX - BX) + (AY - BY) * (CY - BY);

    // 计算最小夹角（使用 fabs 确保角度在 0° 至 90°）
    float cos_theta = fabs(SBA_BC) / (BA * BC);
    if (cos_theta > 1.0) cos_theta = 1.0;  // 防止浮点误差
    angle = acos(cos_theta);

    // 转换为度数并返回
    return angle * 57.3;
}

uint8_t mt9v03x_image_2[MT9V03X_H][MT9V03X_W];
uint8_t mid[MT9V03X_H];
int side_left[MT9V03X_H], side_right[MT9V03X_H];
int feedbackposition=0;
// 输出图像数组
int Left_Line[MT9V03X_H];    // 左边界数组
int Right_Line[MT9V03X_H];   // 右边界数组
int Mid_Line[MT9V03X_H];     // 中线数组
uint8_t mt9v03x_image_eroded[MT9V03X_H][MT9V03X_W];
uint8_t mt9v03x_image_dilated[MT9V03X_H][MT9V03X_W];

static uint8_t last_threshold = 128;
// Define GrayScale
#define GRAY_SCALE 256
uint8_t otsuThreshold(uint8_t *image)
{
    int Pixel_Max = 0;
    int Pixel_Min = 255;
    uint16_t width = MT9V03X_W;
    uint16_t height = MT9V03X_H;
    uint32_t pixelSum = (width * height) / 4; // Subsampled pixel count
    uint32_t gray_sum = 0;
    uint8_t threshold = 0;

    // Initialize pixelCount array to zero
    uint32_t pixelCount[GRAY_SCALE];
    memset(pixelCount, 0, sizeof(pixelCount));

    // Subsample the image by taking every 2nd pixel in both dimensions
    uint8_t* data = image;
    uint32_t idx;
    for (uint16_t i = 0; i < height; i += 2)
    {
        for (uint16_t j = 0; j < width; j += 2)
        {
            idx = i * width + j;
            uint8_t pixel = data[idx];
            pixelCount[pixel]++;
            gray_sum += pixel;

            if (pixel > Pixel_Max) Pixel_Max = pixel;
            if (pixel < Pixel_Min) Pixel_Min = pixel;
        }
    }

    // Calculate total sum only once
    uint32_t total_sum = gray_sum;

    // Compute class probabilities and cumulative sums
    uint32_t sumB = 0;
    uint32_t wB = 0;
    uint32_t wF = 0;

    float varMax = 0.0f;

    // Iterate through all possible thresholds
    for (uint8_t t = Pixel_Min; t <= Pixel_Max; t++)
    {
        wB += pixelCount[t];
        if (wB == 0)
            continue;

        wF = pixelSum - wB;
        if (wF == 0)
            break;

        sumB += t * pixelCount[t];

        // Compute class means
        float mB = (float)sumB / (float)wB;
        float mF = ((float)total_sum - (float)sumB) / (float)wF;

        // Compute between-class variance
        float varBetween = (float)wB * (float)wF * (mB - mF) * (mB - mF);

        // Update threshold if new maximum found
        if (varBetween > varMax)
        {
            varMax = varBetween;
            threshold = t;
        }
    }

    return threshold;
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      使用大津法阈值对图像进行二值化
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void binarizeImage(uint8_t *image, uint8_t *binary_image)
{
    // 计算大津法阈值
    uint8_t threshold = otsuThreshold(image);
    // tft180_show_uint(0,80,threshold,5);
    // 遍历图像并进行二值化
    for (int i = 0; i < MT9V03X_H; i++)
    {
        for (int j = 0; j < MT9V03X_W; j++)
        {
            uint8_t pixel = image[i * MT9V03X_W + j];
            binary_image[i * MT9V03X_W + j] = (pixel < threshold) ? 0 : 255;
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      对二值化图像进行腐蚀操作
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void erodeImage(uint8_t *binary_image, uint8_t *eroded_image)
{
    // 初始化腐蚀后的图像为0
    memset(eroded_image, 0, MT9V03X_H * MT9V03X_W * sizeof(uint8_t));

    for (int i = 1; i < MT9V03X_H - 1; i++)
    {
        for (int j = 1; j < MT9V03X_W - 1; j++)
        {
            // 检查当前像素及其8邻域是否全为白色
            if (binary_image[i * MT9V03X_W + j] == 255 &&
                binary_image[(i - 1) * MT9V03X_W + (j - 1)] == 255 &&
                binary_image[(i - 1) * MT9V03X_W + j] == 255 &&
                binary_image[(i - 1) * MT9V03X_W + (j + 1)] == 255 &&
                binary_image[i * MT9V03X_W + (j - 1)] == 255 &&
                binary_image[i * MT9V03X_W + (j + 1)] == 255 &&
                binary_image[(i + 1) * MT9V03X_W + (j - 1)] == 255 &&
                binary_image[(i + 1) * MT9V03X_W + j] == 255 &&
                binary_image[(i + 1) * MT9V03X_W + (j + 1)] == 255)
            {
                eroded_image[i * MT9V03X_W + j] = 255;
            }
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      对腐蚀后的图像进行膨胀操作
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void dilateImage(uint8_t *eroded_image, uint8_t *dilated_image)
{
    // 初始化膨胀后的图像为0
    memset(dilated_image, 0, MT9V03X_H * MT9V03X_W * sizeof(uint8_t));

    for (int i = 1; i < MT9V03X_H - 1; i++)
    {
        for (int j = 1; j < MT9V03X_W - 1; j++)
        {
            // 如果当前像素或其8邻域中有白色，则设置为白色
            if (eroded_image[i * MT9V03X_W + j] == 255 ||
                eroded_image[(i - 1) * MT9V03X_W + (j - 1)] == 255 ||
                eroded_image[(i - 1) * MT9V03X_W + j] == 255 ||
                eroded_image[(i - 1) * MT9V03X_W + (j + 1)] == 255 ||
                eroded_image[i * MT9V03X_W + (j - 1)] == 255 ||
                eroded_image[i * MT9V03X_W + (j + 1)] == 255 ||
                eroded_image[(i + 1) * MT9V03X_W + (j - 1)] == 255 ||
                eroded_image[(i + 1) * MT9V03X_W + j] == 255 ||
                eroded_image[(i + 1) * MT9V03X_W + (j + 1)] == 255)
            {
                dilated_image[i * MT9V03X_W + j] = 255;
            }
        }
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      使用大津法和形态学操作进行图像二值化
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void otsu_erzhihua(uint8 *image, uint8_t *binary_image)
{
//    system_start();

    // Step 1: 二值化
    binarizeImage(image, binary_image);

//    // Step 2: 腐蚀pow vc(1)
//    erodeImage(binary_image, mt9v03x_image_eroded);
//
//    // Step 3: 膨胀
//    dilateImage(mt9v03x_image_eroded, mt9v03x_image_dilated);

    // Step 4: 将处理后的图像作为最终输出
    memcpy(binary_image, binary_image, MT9V03X_H * MT9V03X_W * sizeof(uint8_t));

//    int ti=system_getval_us();
//    tft180_show_uint(0,100,ti,5);
}

#define IMAGE_W MT9V03X_W
#define IMAGE_H MT9V03X_H
int16 L_start_x, L_start_y, R_start_x, R_start_y;
uint8 left_findflag, right_findflag;
uint8 Boundary_search_end = 0; // 假设搜索终止行（根据需要调整）


//-------------------------------------------------------------------------------------------------------------------
//  @brief      给图像画黑框为八邻域做准备
//  @return     void
//  @since      v1.0
//  Sample usage:   image_draw_rectan(Image_use);
//-------------------------------------------------------------------------------------------------------------------
void image_draw_rectan(uint8(*image)[IMAGE_W])
{
    uint8 i = 0;
    for (i = 0; i < IMAGE_H; i++)
    {
        image[i][0] = 0;
        image[i][1] = 0;
        image[i][IMAGE_W - 1] = 0;
        image[i][IMAGE_W - 2] = 0;
    }
    for (i = 0; i < IMAGE_W; i++)
    {
        image[0][i] = 0;
        image[1][i] = 0;
    }
}


/*---------------------------------------------------------------
 【函    数】search_neighborhood
 【功    能】八邻域找边界
 【参    数】无
 【返 回 值】无
 【注意事项】
 ----------------------------------------------------------------*/
struct LEFT_EDGE
{
    int16 row;  //行坐标
    int16 col;  //列坐标
    uint8 flag; //存在边界的标志
};
struct RIGHT_EDGE
{
    int16 row;  //行坐标
    int16 col;  //列坐标
    uint8 flag; //存在边界的标志
};

struct LEFT_EDGE  L_edge[140];     //左边界结构体
struct RIGHT_EDGE R_edge[140];    //右边界结构体
uint8 L_edge_count=0, R_edge_count = 0;                     //左右边点的个数
uint8 dire_left,dire_right;                                 //记录上一个点的相对位置
uint8 L_search_amount = 140, R_search_amount = 140;  //左右边界搜点时最多允许的点
void search_neighborhood(void)
{
    L_edge_count = 0;  // 左边点个数清零
    R_edge_count = 0;  // 右边点个数清零

    if (left_findflag)  // 如果左边界点存在并找到，则开始爬线
    {
        // 初始化左边界起始点
        L_edge[0].row = L_start_y;
        L_edge[0].col = L_start_x;
        L_edge[0].flag = 1;
        int16_t curr_row = L_start_y;  // 当前行坐标
        int16_t curr_col = L_start_x;  // 当前列坐标
        dire_left = 0;  // 初始化上个边界点的来向

        // 开始搜线，最多取 L_search_amount 个点
        for (int i = 1; i < L_search_amount; i++)
        {
            // 越界检查：行越界（向上或向下）
            if (curr_row + 1 < Boundary_search_end || curr_row > RESULT_ROW - 1) break;

            // 八邻域搜线过程
            if (dire_left != 2 && inverse_perspective_image[curr_row - 1][curr_col - 1] == BLACK_PIXEL && inverse_perspective_image[curr_row - 1][curr_col] == WHITE_PIXEL)  // 左上黑，右边白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 7;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 3 && inverse_perspective_image[curr_row - 1][curr_col + 1] == BLACK_PIXEL && inverse_perspective_image[curr_row][curr_col + 1] == WHITE_PIXEL)  // 右上黑，下边白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col + 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 6;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (inverse_perspective_image[curr_row - 1][curr_col] == BLACK_PIXEL && inverse_perspective_image[curr_row - 1][curr_col + 1] == WHITE_PIXEL)  // 正上黑，右白
            {
                curr_row = curr_row - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 0;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 5 && inverse_perspective_image[curr_row][curr_col - 1] == BLACK_PIXEL && inverse_perspective_image[curr_row - 1][curr_col - 1] == WHITE_PIXEL)  // 正左黑，上白
            {
                curr_col = curr_col - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 4;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 4 && inverse_perspective_image[curr_row][curr_col + 1] == BLACK_PIXEL && inverse_perspective_image[curr_row + 1][curr_col + 1] == WHITE_PIXEL)  // 正右黑，下白
            {
                curr_col = curr_col + 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 5;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 6 && inverse_perspective_image[curr_row + 1][curr_col - 1] == BLACK_PIXEL && inverse_perspective_image[curr_row][curr_col - 1] == WHITE_PIXEL)  // 左下黑，上白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col - 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 3;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else if (dire_left != 7 && inverse_perspective_image[curr_row + 1][curr_col + 1] == BLACK_PIXEL && inverse_perspective_image[curr_row + 1][curr_col] == WHITE_PIXEL)  // 右下黑，左白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col + 1;
                L_edge_count = L_edge_count + 1;
                dire_left = 2;
                L_edge[i].row = curr_row;
                L_edge[i].col = curr_col;
                L_edge[i].flag = 1;
            }
            else
            {
                break;  // 未找到边界点，退出循环
            }
        }
    }

    if (right_findflag)  // 如果右边界存在并找到，则开始爬线
    {
        // 初始化右边界起始点
        R_edge[0].row = R_start_y;
        R_edge[0].col = R_start_x;
        R_edge[0].flag = 1;
        int16_t curr_row = R_start_y;
        int16_t curr_col = R_start_x;
        dire_right = 0;

        // 开始搜线，最多取 R_search_amount 个点
        for (int i = 1; i < R_search_amount; i++)
        {
            // 越界检查：行越界（向上或向下）
            if (curr_row < Boundary_search_end || curr_row > RESULT_ROW - 1 || curr_row + 1 < Boundary_search_end) break;

            // 八邻域爬线过程
            if (curr_col < RESULT_COL && dire_right != 3 && inverse_perspective_image[curr_row - 1][curr_col + 1] == BLACK_PIXEL && inverse_perspective_image[curr_row - 1][curr_col] == WHITE_PIXEL)  // 右上黑，左白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col + 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 6;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 2 && inverse_perspective_image[curr_row - 1][curr_col - 1] == BLACK_PIXEL && inverse_perspective_image[curr_row][curr_col - 1] == WHITE_PIXEL)  // 左上黑，下白
            {
                curr_row = curr_row - 1;
                curr_col = curr_col - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 7;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (inverse_perspective_image[curr_row - 1][curr_col] == BLACK_PIXEL && inverse_perspective_image[curr_row - 1][curr_col - 1] == WHITE_PIXEL)  // 正上黑，左白
            {
                curr_row = curr_row - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 0;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 4 && inverse_perspective_image[curr_row][curr_col + 1] == BLACK_PIXEL && inverse_perspective_image[curr_row - 1][curr_col + 1] == WHITE_PIXEL)  // 正右黑，上白
            {
                curr_col = curr_col + 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 5;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 5 && inverse_perspective_image[curr_row][curr_col - 1] == BLACK_PIXEL && inverse_perspective_image[curr_row + 1][curr_col - 1] == WHITE_PIXEL)  // 正左黑，下白
            {
                curr_col = curr_col - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 4;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 6 && inverse_perspective_image[curr_row + 1][curr_col - 1] == BLACK_PIXEL && inverse_perspective_image[curr_row + 1][curr_col] == WHITE_PIXEL)  // 左下黑，右白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col - 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 3;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else if (dire_right != 7 && inverse_perspective_image[curr_row + 1][curr_col + 1] == BLACK_PIXEL && inverse_perspective_image[curr_row][curr_col + 1] == WHITE_PIXEL)  // 右下黑，上白
            {
                curr_row = curr_row + 1;
                curr_col = curr_col + 1;
                R_edge_count = R_edge_count + 1;
                dire_right = 2;
                R_edge[i].row = curr_row;
                R_edge[i].col = curr_col;
                R_edge[i].flag = 1;
            }
            else
            {
                break;  // 未找到边界点，退出循环
            }
        }
    }
}

/*---------------------------------------------------------------
 【函    数】clear_find_point
 【功    能】八邻域边界初始化
 【参    数】无
 【返 回 值】
 【注意事项】
 ----------------------------------------------------------------*/
void clear_find_point(void)
{
    for(int i = 0;i<L_edge_count;i++)
    {
        L_edge[i].row = 0;
        L_edge[i].col = 0;
        L_edge[i].flag = 0;
    }
    for(int i = 0;i<R_edge_count;i++)
    {
        R_edge[i].row = 0;
        R_edge[i].col = 0;
        R_edge[i].flag = 0;
    }
}
/*---------------------------------------------------------------
 【函    数】calc_diff
 【功    能】差比和
 【参    数】无
 【返 回 值】
 【注意事项】约放大128倍
 ----------------------------------------------------------------*/
int16 calc_diff(int16 x, int16 y)
{
    return ( ((x-y)<<7)/(x+y) );
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      限幅
//  @param      x               被限幅的数据
//  @param      y               限幅范围(数据会被限制在-y至+y之间)
//  @return     float           限幅之后的数据
//  Sample usage:               float dat = limit(500,300);//数据被限制在-300至+300之间  因此返回的结果是300
//-------------------------------------------------------------------------------------------------------------------
float limit(float x, int32 y)
{
    if(x>y)             return (float)y;
    else if(x<-y)       return (float)(-y);
    else                return x;
}
void draw_eight_neighborhood_boundaries(void) {
    int i;

    // 绘制左边界粗线
    for (i = 0; i < L_edge_count; i++) {
        if (L_edge[i].flag == 1) {  // 仅绘制有效的边界点
            // 获取边界点的坐标
            int y = L_edge[i].row;
            int x = L_edge[i].col;

            // 限制 y 和 x 在显示屏范围内
            y = (y < 0) ? 0 : ((y > 127) ? 127 : y);
            x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

            // 确保绘制点及其左右点在屏幕范围内
            if (x >= 1 && x <= 158) {
                tft180_draw_point(x - 1, y, RGB565_GREEN);  // 左点
                tft180_draw_point(x, y, RGB565_GREEN);      // 中心点
                tft180_draw_point(x + 1, y, RGB565_GREEN);  // 右点
            }
        }
    }

    // 绘制右边界粗线
    for (i = 0; i < R_edge_count; i++) {
        if (R_edge[i].flag == 1) {  // 仅绘制有效的边界点
            // 获取边界点的坐标
            int y = R_edge[i].row;
            int x = R_edge[i].col;

            // 限制 y 和 x 在显示屏范围内
            y = (y < 0) ? 0 : ((y > 127) ? 127 : y);
            x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

            // 确保绘制点及其左右点在屏幕范围内
            if (x >= 1 && x <= 158) {
                tft180_draw_point(x - 1, y, RGB565_YELLOW); // 左点
                tft180_draw_point(x, y, RGB565_YELLOW);     // 中心点
                tft180_draw_point(x + 1, y, RGB565_YELLOW); // 右点
            }
        }
    }
}
int16 L_corner_flag = 0;//左拐点存在标志
int16 L_corner_row = 0;//左拐点所在行
int16 L_corner_col = 0;//左拐点所在列
int L_corner_angle = 0;//左拐点角度
int16 R_corner_flag = 0;//右拐点存在标志
int16 R_corner_row = 0;//右拐点所在行
int16 R_corner_col = 0;//右拐点所在列
int R_corner_angle = 0;//右拐点角度
uint8 enable_L_corner=1,enable_R_corner=1;
void get_turning_point(void)
{
    L_corner_flag = 0;// 初始化变量
    L_corner_row = 0;
    L_corner_col = 0;
    L_corner_angle = 0;
    if(enable_L_corner) //如果使能搜索左拐点
    {
        if(L_edge_count > 9&&L_start_y>=IMAGE_H/2)
        {
            for(int i = 0; i<L_edge_count-9;i++)
            {
                if(L_edge[i+8].row>5)
                {
                    if((L_edge[i].col - L_edge[i + 4].col) * (L_edge[i + 8].col - L_edge[i + 4].col) +
                       (L_edge[i].row - L_edge[i + 4].row) * (L_edge[i + 8].row - L_edge[i + 4].row) >= 0) //初步确认为锐角或者直角 向量法
                    {
                        L_corner_angle = Get_angle(L_edge[i].col, L_edge[i].row, L_edge[i + 4].col, L_edge[i + 4].row, L_edge[i + 8].col, L_edge[i + 8].row); //求角度
                        if(L_edge[i+4].col>L_edge[i+8].col&&L_corner_angle>=28&&L_corner_angle<=110)
                        {
                            L_corner_flag = 1;
                            L_corner_row = L_edge[i+4].row;
                            L_corner_col = L_edge[i+4].col;
                            break;
                        }
                    }
                }
            }
        }
    }
    R_corner_flag = 0;//初始化变量
    R_corner_row = 0;
    R_corner_col = 0;
    R_corner_angle = 0;
    if(enable_R_corner)    //如果使能搜索右拐点
    {
        if(R_edge_count > 9&&R_start_y>=IMAGE_H/2)
        {
            for(int i = 0; i<R_edge_count-9;i++)
            {
                if(R_edge[i+8].row>5)
                {
                    if((R_edge[i].col - R_edge[i + 4].col) * (R_edge[i + 8].col - R_edge[i + 4].col) +
                    (R_edge[i].row - R_edge[i + 4].row) * (R_edge[i + 8].row - R_edge[i + 4].row) >= 0) //初步确认为锐角或者直角 向量法
                    {
                        R_corner_angle = Get_angle(R_edge[i].col, R_edge[i].row, R_edge[i + 4].col, R_edge[i + 4].row, R_edge[i + 8].col, R_edge[i + 8].row); //求角度
                        if(R_edge[i+8].col>R_edge[i+4].col&&R_corner_angle>=28&&R_corner_angle<=110)
                        {
                            R_corner_flag = 1;
                            R_corner_row = R_edge[i+4].row;
                            R_corner_col = R_edge[i+4].col;
                            break;
                        }
                    }
                }
            }
        }
    }
}
void balinyu() {
    // 首先更新逆透视图像
    update_inverse_perspective_image();
    
    // 步骤 1 & 2：画黑框到逆透视图像上，但保留底行
    for (int i = 0; i < RESULT_ROW; i++) {
        inverse_perspective_image[i][0] = BLACK_PIXEL;
        inverse_perspective_image[i][RESULT_COL-1] = BLACK_PIXEL;
    }
    for (int i = 0; i < RESULT_COL; i++) {
        inverse_perspective_image[0][i] = BLACK_PIXEL;
        // 不再将底行设置为黑色
        // inverse_perspective_image[RESULT_ROW-1][i] = BLACK_PIXEL;
    }

    // 使用新的八邻域巡线逻辑
    line_init();  // 初始化线条
    
    // 运行边线检测
    int result = find_edge_line();
    
    // 对左右边线进行滤波处理
    filter_lines(0, 5);  // 左边线平滑，强度5
    filter_lines(1, 5);  // 右边线平滑，强度5
    
    // 根据左右边线计算中线（使用更高效的算法）
    // 先创建行索引数组
    int left_row_map[120] = {0};  // 存储每行对应的左边线索引
    int right_row_map[120] = {0}; // 存储每行对应的右边线索引
    
    // 填充行索引映射
    for (int i = 0; i <= left_lenth; i++) {
        int row = left_line_raw[i][0];
        if (row >= 0 && row < 120) {
            left_row_map[row] = i + 1; // +1以区分未找到(0)和索引0
        }
    }
    
    for (int i = 0; i <= right_lenth; i++) {
        int row = right_line_raw[i][0];
        if (row >= 0 && row < 120) {
            right_row_map[row] = i + 1; // +1以区分未找到(0)和索引0
        }
    }
    
    // 扫描所有行，查找同时有左右边界的行
    mid_lenth = 0;
    for (int row = 0; row < 120; row++) {
        if (left_row_map[row] > 0 && right_row_map[row] > 0) {
            int left_idx = left_row_map[row] - 1;
            int right_idx = right_row_map[row] - 1;
            
            mid_line_raw[mid_lenth][0] = row; // 行坐标
            mid_line_raw[mid_lenth][1] = (left_line_raw[left_idx][1] + right_line_raw[right_idx][1]) / 2; // 列坐标取平均
            mid_lenth++;
            
            if (mid_lenth >= 450) break; // 安全检查，防止数组越界
        }
    }
    
    // 对中线进行滤波处理
    filter_lines(2, 5);  // 中线平滑，强度5
    
    // 转换为原有的 Left_Line, Right_Line, Mid_Line 格式，以便与现有代码兼容
    for (int i = 0; i < MT9V03X_H; i++) {
        Left_Line[i] = -1;  // 默认设为-1表示未找到
        Right_Line[i] = -1;
        Mid_Line[i] = -1;
    }
    
    // 填充左边线数据
    for (int i = 0; i <= left_lenth; i++) {
        int row = left_line_raw[i][0];
        if (row >= 0 && row < MT9V03X_H) {
            Left_Line[row] = left_line_raw[i][1];
        }
    }
    
    // 填充右边线数据
    for (int i = 0; i <= right_lenth; i++) {
        int row = right_line_raw[i][0];
        if (row >= 0 && row < MT9V03X_H) {
            Right_Line[row] = right_line_raw[i][1];
        }
    }
    
    // 填充中线数据
    for (int i = 0; i <= mid_lenth; i++) {
        int row = mid_line_raw[i][0];
        if (row >= 0 && row < MT9V03X_H) {
            Mid_Line[row] = mid_line_raw[i][1];
        }
    }
    
    // 将角点信息转换为旧系统的格式
    L_corner_row = left_corner[0][0] > 0 ? left_line_raw[left_corner[0][0]][0] : 0;
    L_corner_col = left_corner[0][0] > 0 ? left_line_raw[left_corner[0][0]][1] : 0;
    L_corner_flag = left_corner[0][0] > 0 ? 1 : 0;
    
    R_corner_row = right_corner[0][0] > 0 ? right_line_raw[right_corner[0][0]][0] : 0;
    R_corner_col = right_corner[0][0] > 0 ? right_line_raw[right_corner[0][0]][1] : 0;
    R_corner_flag = right_corner[0][0] > 0 ? 1 : 0;
}

//-------------------------------------------------------------------------------------------------------------------
//  新八邻域巡线变量定义
//-------------------------------------------------------------------------------------------------------------------

// 边线数据存储
int left_line_raw[500][2];    // 原始左边线点集 [y,x]
int right_line_raw[500][2];   // 原始右边线点集
int mid_line_raw[500][2];     // 原始中线点集
int left_line[500][2];        // 重采样后左边线
int right_line[500][2];       // 重采样后右边线
int mid_line[500][2];         // 重采样后中线

// 长度和起始索引
int left_lenth = 0;
int right_lenth = 0;
int left_start = 0;
int right_start = 0;
int mid_lenth = 0;

// 角点存储
int left_corner[50][2];     // 左边线角点 [角点序号][0:下标/1:类型]
int right_corner[50][2];    // 右边线角点

// 道路宽度信息
int left_width[120] = {0};   // 左边线位置
int right_width[120] = {0};  // 右边线位置
int road_width[120] = {0};   // 道路宽度

// 方向向量和状态
int dy[4] = {-1, 0, 1, 0};  // 上、右、下、左
int dx[4] = {0, 1, 0, -1};
int direction_l = 0;        // 左边线搜索方向
int direction_r = 0;        // 右边线搜索方向
int left_error_sum = 0;
int right_error_sum = 0;

// 状态计数
int left_straight = 0;
int right_straight = 0;
int left_lose = 0;
int right_lose = 0;
int mid_lose = 0;

//-------------------------------------------------------------------------------------------------------------------
//  获取逆透视图像像素值函数
//-------------------------------------------------------------------------------------------------------------------
uint8_t get_image_sum_real(int row, int col)
{
    // 边界检查
    if (row < 0 || row >= RESULT_ROW || col < 0 || col >= RESULT_COL)
    {
        return 0; // 边界外返回黑色
    }
    
    return inverse_perspective_image[row][col];
}

//-------------------------------------------------------------------------------------------------------------------
//  线条初始化函数 - 为160×120分辨率图像优化
//-------------------------------------------------------------------------------------------------------------------
void line_init(void)
{
    int max_len = 0;
    int curr_len = 0;
    int curr_start = 0;
    int start_x_left = -1;
    int start_x_right = -1;

    // 扫描图像118行，寻找最宽的白色区域
    for (int x = 0; x < 160; ++x)
    {
        if (get_image_sum_real(118, x) != 0)  // 白色像素
        { 
            if (curr_len == 0)
            {
                curr_start = x;
            }
            curr_len++;
        }
        else
        {
            if (curr_len > max_len)
            {
                max_len = curr_len;
                start_x_left = curr_start;
                start_x_right = x - 1;
            }
            curr_len = 0;
        }
    }

    // 处理末尾是白色的情况
    if (curr_len > max_len)
    {
        max_len = curr_len;
        start_x_left = curr_start;
        start_x_right = 159;
    }
    
    // 如果找不到起始点，使用默认值
    if (start_x_left == -1) start_x_left = 40;  // 默认左边界位置
    if (start_x_right == -1) start_x_right = 120; // 默认右边界位置
    
    // 确保左右边界的合理性
    if (start_x_left >= start_x_right) {
        start_x_left = 40;
        start_x_right = 120;
    }
    
    // 设置左边线起点
    left_line_raw[0][0] = 118;          // 行坐标改为118
    left_line_raw[0][1] = start_x_left; // 列坐标
    left_lenth = 0;
    left_start = 0;
    
    // 设置右边线起点
    right_line_raw[0][0] = 118;        // 行坐标改为118
    right_line_raw[0][1] = start_x_right;
    right_lenth = 0;
    right_start = 0;
    
    // 设置中线起点
    mid_line_raw[0][0] = 118;          // 行坐标改为118
    mid_line_raw[0][1] = (start_x_left + start_x_right) / 2;
    mid_lenth = 0;
    
    // 重置角点数据
    left_corner[0][0] = 0;
    right_corner[0][0] = 0;
    
    // 重置方向向量
    direction_l = 0;  // 初始向上
    direction_r = 0;  // 初始向上
    
    // 重置错误计数
    left_error_sum = 0;
    right_error_sum = 0;
    
    // 重置直线度和缺失计数
    left_straight = 0;
    right_straight = 0;
    left_lose = 0;
    right_lose = 0;
    mid_lose = 0;
}

//-------------------------------------------------------------------------------------------------------------------
//  左边线生长函数 - 为160×120分辨率图像优化
//-------------------------------------------------------------------------------------------------------------------
void line_grow_left(void)
{
    // 如果错误计数过高，直接返回，防止无效搜索
    if (left_error_sum > 15) {
        return;
    }
    
    int y = left_line_raw[left_lenth][0];
    int x = left_line_raw[left_lenth][1];
    
    // 计算左方、左前方坐标
    int left_dir = (direction_l + 3) % 4; // 左方
    int left_y = y + dy[left_dir];
    int left_x = x + dx[left_dir];
    int left_front_y = y + dy[left_dir] + dy[direction_l]; // 左前方
    int left_front_x = x + dx[left_dir] + dx[direction_l];
    
    // 边界检查
    if (left_y < 0 || left_y >= 120 || left_x < 0 || left_x >= 160 ||
        left_front_y < 0 || left_front_y >= 120 || left_front_x < 0 || left_front_x >= 160) {
        direction_l = (direction_l + 1) % 4;
        left_error_sum++;
        return;
    }
    
    if (get_image_sum_real(left_y, left_x) == 0 && get_image_sum_real(left_front_y, left_front_x) == 0)
    {
        // 左和左前都黑，直走
        int front_y = y + dy[direction_l];
        int front_x = x + dx[direction_l];
        
        // 边界检查
        if (front_y < 0 || front_y >= 120 || front_x < 0 || front_x >= 160) {
            direction_l = (direction_l + 1) % 4;
            return;
        }
        
        if (get_image_sum_real(front_y, front_x) == 255)
        {
            left_error_sum = 0;
            y = front_y;
            x = front_x;
            
            left_line_raw[left_lenth + 1][0] = y;
            left_line_raw[left_lenth + 1][1] = x;
            left_lenth++;
        }
        else
        {
            direction_l = (direction_l + 1) % 4;
        }
    }
    else if (get_image_sum_real(left_y, left_x) == 0 && get_image_sum_real(left_front_y, left_front_x) == 255)
    {
        // 左黑 左前白，左转走
        left_error_sum = 0;
        y = left_front_y;
        x = left_front_x;
        direction_l = (direction_l + 3) % 4;
        
        left_line_raw[left_lenth + 1][0] = y;
        left_line_raw[left_lenth + 1][1] = x;
        left_lenth++;
    }
    else
    {
        direction_l = (direction_l + 1) % 4;
        left_error_sum++;
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  右边线生长函数 - 为160×120分辨率图像优化
//-------------------------------------------------------------------------------------------------------------------
void line_grow_right(void)
{
    // 如果错误计数过高，直接返回，防止无效搜索
    if (right_error_sum > 15) {
        return;
    }
    
    int y = right_line_raw[right_lenth][0];
    int x = right_line_raw[right_lenth][1];

    // 计算右方、右前方坐标
    int right_dir = (direction_r + 1) % 4; // 右方
    int right_y = y + dy[right_dir];
    int right_x = x + dx[right_dir];
    int right_front_y = y + dy[right_dir] + dy[direction_r]; // 右前方
    int right_front_x = x + dx[right_dir] + dx[direction_r];
    
    // 边界检查
    if (right_y < 0 || right_y >= 120 || right_x < 0 || right_x >= 160 ||
        right_front_y < 0 || right_front_y >= 120 || right_front_x < 0 || right_front_x >= 160) {
        direction_r = (direction_r + 3) % 4;
        right_error_sum++;
        return;
    }

    if (get_image_sum_real(right_y, right_x) == 0 && get_image_sum_real(right_front_y, right_front_x) == 0)
    {
        // 右和右前都黑，直走
        int front_y = y + dy[direction_r];
        int front_x = x + dx[direction_r];
        
        // 边界检查
        if (front_y < 0 || front_y >= 120 || front_x < 0 || front_x >= 160) {
            direction_r = (direction_r + 3) % 4;
            return;
        }
        
        if (get_image_sum_real(front_y, front_x) == 255)
        {
            right_error_sum = 0;
            y = front_y;
            x = front_x;

            right_line_raw[right_lenth + 1][0] = y;
            right_line_raw[right_lenth + 1][1] = x;
            right_lenth++;
        }
        else
        {
            direction_r = (direction_r + 3) % 4;
        }
    }
    else if (get_image_sum_real(right_y, right_x) == 0 && get_image_sum_real(right_front_y, right_front_x) == 255)
    {
        // 右黑 右前白，右转走
        right_error_sum = 0;
        y = right_front_y;
        x = right_front_x;
        direction_r = (direction_r + 1) % 4;

        right_line_raw[right_lenth + 1][0] = y;
        right_line_raw[right_lenth + 1][1] = x;
        right_lenth++;
    }
    else
    {
        direction_r = (direction_r + 3) % 4;
        right_error_sum++;
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  边线检测函数 - 为160×120分辨率图像优化
//-------------------------------------------------------------------------------------------------------------------
int find_edge_line(void)
{
    // 初始化道路宽度数组
    for (int i = 0; i < 120; i++)
    {
        left_width[i] = 0;
        right_width[i] = 0;
    }
    
    // 安全变量：最大迭代次数
    int max_iterations = 1000;
    int iteration_count = 0;

    // 左右线同时生长，直到相遇或者超出边界
    while (((left_line_raw[left_lenth][0] != right_line_raw[right_lenth][0]) || 
            (left_line_raw[left_lenth][1] != right_line_raw[right_lenth][1])) &&
            (left_lenth < 450) && (right_lenth < 450) && 
            (left_line_raw[left_lenth][0] != 0 || right_line_raw[right_lenth][0] != 0) &&
            (iteration_count < max_iterations)) // 添加迭代次数限制
    {
        iteration_count++; // 计数器递增
        
        // 当错误计数过高时，退出循环，防止无效搜索
        if (left_error_sum > 20 || right_error_sum > 20) {
            break;
        }
        
        // 优先生长在更低位置的边线
        if (left_line_raw[left_lenth][0] >= right_line_raw[right_lenth][0] && 
            left_line_raw[left_lenth][0] != 0)
        {
            line_grow_left();
            // 记录该行的左边界位置
            int row = left_line_raw[left_lenth][0];
            if (row >= 0 && row < 120) { // 边界检查
                left_width[row] = left_line_raw[left_lenth][1];
            }
        }
        else if (right_line_raw[right_lenth][0] != 0)
        {
            line_grow_right();
            // 记录该行的右边界位置
            int row = right_line_raw[right_lenth][0];
            if (row >= 0 && row < 120) { // 边界检查
                right_width[row] = right_line_raw[right_lenth][1];
            }
        }
    }

    // 检查是否成功检测到足够的边线点
    if (left_lenth < 5 && right_lenth < 5)
    {
        return -1;  // 边线检测失败
    }
    return 0;       // 边线检测成功
}

//-------------------------------------------------------------------------------------------------------------------
//  边线平滑函数 - 为160×120分辨率图像优化
//-------------------------------------------------------------------------------------------------------------------
void filter_lines(int type, int strength)
{
    // 滤波强度必须是奇数且不小于3
    if (strength < 3)
        strength = 3;
    if (strength % 2 == 0)
        strength += 1;
    int half = strength / 2;

    // 指针变量
    int (*line_raw)[2];
    int *length_ptr;

    // 中间缓冲数组
    float tmp[450][2];  // 调整为新分辨率下合适的大小

    // 根据类型选择对应的线
    if (type == 0)
    {
        line_raw = left_line_raw;
        length_ptr = &left_lenth;
    }
    else if (type == 1)
    {
        line_raw = right_line_raw;
        length_ptr = &right_lenth;
    }
    else
    {
        line_raw = mid_line_raw;
        length_ptr = &mid_lenth;
    }

    int num = *length_ptr; // 最远点下标
    
    // 滑动平均过程
    for (int i = 0; i <= num; i++)
    {
        float sum_y = 0, sum_x = 0;
        int count = 0;
        for (int j = -half; j <= half; j++)
        {
            int idx = i + j;
            if (idx >= 0 && idx <= num)
            {
                sum_y += line_raw[idx][0];
                sum_x += line_raw[idx][1];
                count++;
            }
        }
        tmp[i][0] = sum_y / count;
        tmp[i][1] = sum_x / count;
    }

    // 将滤波结果写回原始数组
    for (int i = 0; i <= num; i++)
    {
        line_raw[i][0] = (int)(tmp[i][0] + 0.5f);
        line_raw[i][1] = (int)(tmp[i][1] + 0.5f);
    }
}

//-------------------------------------------------------------------------------------------------------------------
//  @brief      绘制新八邻域巡线的边界线和中线
//  @return     void
//-------------------------------------------------------------------------------------------------------------------
void draw_new_edge_lines(void) {
    int i;

    // 绘制左边界粗线（绿色）
    for (i = 0; i <= left_lenth; i++) {
        // 获取左边线点的坐标
        int y = left_line_raw[i][0];
        int x = left_line_raw[i][1];

        // 限制 y 和 x 在显示屏范围内
        y = (y < 0) ? 0 : ((y > 119) ? 119 : y);
        x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

        // 确保绘制点及其左右点在屏幕范围内
        if (x >= 1 && x <= 158) {
            tft180_draw_point(x - 1, y, RGB565_GREEN);  // 左点
            tft180_draw_point(x, y, RGB565_GREEN);      // 中心点
            tft180_draw_point(x + 1, y, RGB565_GREEN);  // 右点
        }
    }

    // 绘制右边界粗线（黄色）
    for (i = 0; i <= right_lenth; i++) {
        // 获取右边线点的坐标
        int y = right_line_raw[i][0];
        int x = right_line_raw[i][1];

        // 限制 y 和 x 在显示屏范围内
        y = (y < 0) ? 0 : ((y > 119) ? 119 : y);
        x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

        // 确保绘制点及其左右点在屏幕范围内
        if (x >= 1 && x <= 158) {
            tft180_draw_point(x - 1, y, RGB565_YELLOW); // 左点
            tft180_draw_point(x, y, RGB565_YELLOW);     // 中心点
            tft180_draw_point(x + 1, y, RGB565_YELLOW); // 右点
        }
    }

    // 绘制中线粗线（红色）
    for (i = 0; i <= mid_lenth; i++) {
        // 获取中线点的坐标
        int y = mid_line_raw[i][0];
        int x = mid_line_raw[i][1];

        // 限制 y 和 x 在显示屏范围内
        y = (y < 0) ? 0 : ((y > 119) ? 119 : y);
        x = (x < 0) ? 0 : ((x > 159) ? 159 : x);

        // 确保绘制点及其左右点在屏幕范围内
        if (x >= 1 && x <= 158) {
            tft180_draw_point(x - 1, y, RGB565_RED);   // 左点
            tft180_draw_point(x, y, RGB565_RED);       // 中心点
            tft180_draw_point(x + 1, y, RGB565_RED);   // 右点
        }
    }
    
    // 如果找到了左拐点，标记左拐点位置（蓝色）
    if (left_corner[0][0] > 0) {
        int idx = left_corner[0][0];
        int y = left_line_raw[idx][0];
        int x = left_line_raw[idx][1];
        
        // 绘制一个蓝色小十字标记拐点
        for (int j = -2; j <= 2; j++) {
            if (x + j >= 0 && x + j < 160) {
                tft180_draw_point(x + j, y, RGB565_BLUE);
            }
            if (y + j >= 0 && y + j < 120) {
                tft180_draw_point(x, y + j, RGB565_BLUE);
            }
        }
    }
    
    // 如果找到了右拐点，标记右拐点位置（蓝色）
    if (right_corner[0][0] > 0) {
        int idx = right_corner[0][0];
        int y = right_line_raw[idx][0];
        int x = right_line_raw[idx][1];
        
        // 绘制一个蓝色小十字标记拐点
        for (int j = -2; j <= 2; j++) {
            if (x + j >= 0 && x + j < 160) {
                tft180_draw_point(x + j, y, RGB565_BLUE);
            }
            if (y + j >= 0 && y + j < 120) {
                tft180_draw_point(x, y + j, RGB565_BLUE);
            }
        }
    }
}
