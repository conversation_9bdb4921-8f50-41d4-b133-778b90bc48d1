###############################################################################
#
# IAR ELF Linker V9.40.1.364/W64 for ARM                  03/Aug/2025  22:40:37
# Copyright 2007-2023 IAR Systems AB.
#
#    Output file  =
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out
#    Map file     =
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\List\RT106X.map
#    Command line =
#        -f
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out.rsp
#        (C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\board.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\ce.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\clock_config.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\diskio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\evkmimxrt1064_flexspi_nor_config.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\evkmimxrt1064_sdram_ini_dcd.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\ff.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\ffsystem.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\ffunicode.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_adapter_lpuart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_adc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_adc_etc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_aipstz.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_aoi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_assert.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_bee.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_cache.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_clock.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_cmp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_common.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_common_arm.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_component_generic_list.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_csi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_dcdc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_dcp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_debug_console.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_dmamux.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_elcdif.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_enc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_enet.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_ewm.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexcan.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexcan_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_camera.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_camera_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_i2c_master.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_i2s.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_i2s_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_mculcd.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_mculcd_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_spi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_spi_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_uart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexio_uart_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexram.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexram_allocate.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexspi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_flexspi_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_flexspi_nor_boot.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_gpc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_gpio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_gpt.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_kpp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpi2c.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpi2c_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpspi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpspi_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpuart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_lpuart_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_ocotp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\fsl_os_abstraction_bm.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pit.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pmu.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pwm.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_pxp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_qtmr.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_romapi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_rtwdog.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_sai.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_sai_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sd.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir\fsl_sd_disk.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sdmmc_common.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sdmmc_host.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\fsl_sdmmc_osa.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_semc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_snvs_hp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_snvs_lp.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_spdif.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_spdif_edma.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_src.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_tempmon.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_trng.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_tsc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_usdhc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_wdog.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_xbara.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir\fsl_xbarb.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\hanzi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\init.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir\isr.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\laoban.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir\main.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\menu.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir\sdmmc_config.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir\seekfree_assistant.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir\seekfree_assistant_interface.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\siyuanshu.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\startup_MIMXRT1064.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\sxt.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir\system_MIMXRT1064.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\uart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_cdc_acm.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_ch9.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_class.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_dci.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_descriptor.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_device_ehci.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir\usb_phy.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\xiangzi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\yuansu.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_clock.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_debug.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_fifo.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_font.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_function.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_interrupt.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir\zf_common_vector.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_absolute_encoder.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ble6a20.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_bluetooth_ch9141.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_camera.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_dl1a.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_dl1b.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_gnss.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_icm20602.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_imu660ra.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_imu963ra.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ips114.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ips200.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_key.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_mpu6050.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_mt9v03x.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_mt9v03x_flexio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_oled.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_ov7725.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_scc8660.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_scc8660_flexio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_tft180.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_tsl1401.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_type.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_virtual_oscilloscope.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_wifi_spi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_wifi_uart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir\zf_device_wireless_uart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_adc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_csi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_delay.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_encoder.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_exti.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_flash.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_flexio_csi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_gpio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_iic.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_pit.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_pwm.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_romapi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_sdio.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_soft_iic.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_soft_spi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_spi.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_timer.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_uart.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir\zf_driver_usb_cdc.o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir\zhuangtaiji.o
#        --no_out_extension -o
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out
#        --map
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\List\RT106X.map
#        --config
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\..\..\IAR\icf\MIMXRT1064xxxxx_flexspi_nor.icf
#        --semihosting
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\..\..\..\libraries\zf_device\zf_device_config.a
#        --entry __iar_program_start --vfe --text_out locale --cpu=Cortex-M7
#        --fpu=VFPv5_d16) --dependencies=n
#        C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Exe\RT106X.out.iar_deps
#
###############################################################################

*******************************************************************************
*** RUNTIME MODEL ATTRIBUTES
***

CppFlavor       = *
__CPP_Runtime   = 1
__Heap_Handler  = Basic
__SystemLibrary = DLib
__dlib_version  = 6


*******************************************************************************
*** HEAP SELECTION
***

The basic heap was selected because --advanced_heap
was not specified and the application did not appear to
be primarily optimized for speed.


*******************************************************************************
*** PLACEMENT SUMMARY
***

"A0":  place at address 0x7000'2000 { ro section .intvec };
"A2":  place at address 0x7000'0000 { section .boot_hdr.conf };
"A3":  place at address 0x7000'1000 { section .boot_hdr.ivt };
"A4":  place at address 0x7000'1020 { ro section .boot_hdr.boot_data };
"A5":  place at address 0x7000'1030 { ro section .boot_hdr.dcd_data };
"P1":  place in [from 0x7000'2000 to 0x7000'23ff] |
                [from 0x7000'2400 to 0x703f'ffff] { ro };
define block RW { first rw, section m_usb_dma_init_data };
"P2":  place in [from 0x8000'0000 to 0x8000'03ff] |
                [from 0x8000'0400 to 0x81df'ffff] { block RW };
define block SDRAM_VAR
   with alignment = 8 { section SDRAM_CACHE, section SDRAM_CACHE.init };
"P11": place in [from 0x8000'0000 to 0x8000'03ff] |
                [from 0x8000'0400 to 0x81df'ffff] { block SDRAM_VAR };
define block ZI
   with alignment = 32 { first zi, section m_usb_dma_noninit_data };
"P3":  place in [from 0x2000'0000 to 0x2006'efff] { block ZI };
"P4":  place in [from 0x2000'0000 to 0x2006'efff] { section .data };
"P5":  place in [from 0x2000'0000 to 0x2006'efff] { section .textrw };
define block DTCM_VAR
   with alignment = 8 { section NonCacheable, section NonCacheable.init };
"P9":  place in [from 0x2000'0000 to 0x2006'efff] { block DTCM_VAR };
define block HEAP with size = 1K, alignment = 8 { };
"P6":  place in [from 0x2000'0000 to 0x2006'efff] { last block HEAP };
define block CSTACK with size = 4K, alignment = 8 { };
"P7":  place in [from 0x2006'f000 to 0x2006'ffff] { block CSTACK };
initialize by copy { rw, section .textrw };
initialize by copy { ro }
   except {
      ro section .intvec, ro object system_MIMXRT1064.o,
      ro object startup_MIMXRT1064.o, section .boot_hdr.conf,
      section .boot_hdr.ivt, section .boot_hdr.boot_data,
      section .boot_hdr.dcd_data };
keep {
   section .boot_hdr.conf, section .boot_hdr.ivt, section .boot_hdr.boot_data,
   section .boot_hdr.dcd_data };

No sections matched the following patterns:

  section .intvec_RAM              in "A1"
  section .textrw                  in "P3-P5|P9"
  section ITCM_NonCacheable        in block ITCM_VAR
  section ITCM_NonCacheable.init   in block ITCM_VAR
  section NonCacheable.init        in block DTCM_VAR
  section OCRAM_CACHE              in block OCRAM_VAR
  section OCRAM_CACHE.init         in block OCRAM_VAR
  section SDRAM_CACHE              in block SDRAM_VAR
  section SDRAM_CACHE.init         in block SDRAM_VAR
  section SDRAM_NonCacheable       in block SDRAM_NCACHE_VAR
  section SDRAM_NonCacheable.init  in block SDRAM_NCACHE_VAR
  section m_usb_dma_init_data      in block RW
  section m_usb_dma_noninit_data   in block ZI


  Section              Kind         Address      Size  Object
  -------              ----         -------      ----  ------
"P3-P5|P9", part 1 of 2:                        0xc9e
  P3-P5|P9 s0                   0x2000'0000     0xc9e  <Init block>
    .data              inited   0x2000'0000     0x100  menu.o [1]
    .data              inited   0x2000'0100      0xe0  menu.o [1]
    .data              inited   0x2000'01e0      0x80  menu.o [1]
    .data              inited   0x2000'0260      0x80  menu.o [1]
    .data              inited   0x2000'02e0      0x80  menu.o [1]
    .data              inited   0x2000'0360      0x80  menu.o [1]
    .data              inited   0x2000'03e0      0x80  menu.o [1]
    .data              inited   0x2000'0460      0x80  menu.o [1]
    .data              inited   0x2000'04e0      0x80  menu.o [1]
    .data              inited   0x2000'0560      0x80  menu.o [1]
    .data              inited   0x2000'05e0      0x80  menu.o [1]
    .data              inited   0x2000'0660      0x80  menu.o [1]
    .data              inited   0x2000'06e0      0x80  menu.o [1]
    .data              inited   0x2000'0760      0x80  menu.o [1]
    .data              inited   0x2000'07e0      0x80  menu.o [1]
    .data              inited   0x2000'0860      0x80  menu.o [1]
    .data              inited   0x2000'08e0      0x80  menu.o [1]
    .data              inited   0x2000'0960      0x60  menu.o [1]
    .data              inited   0x2000'09c0      0x54  ce.o [1]
    .data              inited   0x2000'0a14      0x54  ce.o [1]
    .data              inited   0x2000'0a68      0x2c  zf_driver_gpio.o [11]
    .data              inited   0x2000'0a94      0x28  zf_device_mt9v03x.o [10]
    .data              inited   0x2000'0abc      0x24  zf_device_mt9v03x.o [10]
    .data              inited   0x2000'0ae0      0x24  zf_driver_uart.o [11]
    .data              inited   0x2000'0b04      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b20      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b3c      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b58      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b74      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0b90      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0bac      0x1c  zhuangtaiji.o [1]
    .data              inited   0x2000'0bc8      0x14  zf_driver_encoder.o [11]
    .data              inited   0x2000'0bdc      0x14  zf_driver_pwm.o [11]
    .data              inited   0x2000'0bf0      0x14  zf_driver_spi.o [11]
    .data              inited   0x2000'0c04      0x10  sxt.o [1]
    .data              inited   0x2000'0c14      0x10  sxt.o [1]
    .data              inited   0x2000'0c24       0x8  zf_device_imu660ra.o [10]
    .data              inited   0x2000'0c2c       0x4  main.o [7]
    .data              inited   0x2000'0c30       0x4  menu.o [1]
    .data              inited   0x2000'0c34       0x4  menu.o [1]
    .data              inited   0x2000'0c38       0x4  menu.o [1]
    .data              inited   0x2000'0c3c       0x4  menu.o [1]
    .data              inited   0x2000'0c40       0x4  menu.o [1]
    .data              inited   0x2000'0c44       0x4  menu.o [1]
    .data              inited   0x2000'0c48       0x4  menu.o [1]
    .data              inited   0x2000'0c4c       0x4  siyuanshu.o [1]
    .data              inited   0x2000'0c50       0x4  siyuanshu.o [1]
    .data              inited   0x2000'0c54       0x4  siyuanshu.o [1]
    .data              inited   0x2000'0c58       0x4  sxt.o [1]
    .data              inited   0x2000'0c5c       0x4  system_MIMXRT1064.o [5]
    .data              inited   0x2000'0c60       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c64       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c68       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c6c       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c70       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c74       0x4  xiangzi.o [1]
    .data              inited   0x2000'0c78       0x4  zf_common_clock.o [8]
    .data              inited   0x2000'0c7c       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c80       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c84       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c88       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c8c       0x4  zf_device_type.o [10]
    .data              inited   0x2000'0c90       0x2  zf_device_tft180.o [10]
    .data              inited   0x2000'0c92       0x1  main.o [7]
    .data              inited   0x2000'0c93       0x1  main.o [7]
    .data              inited   0x2000'0c94       0x1  main.o [7]
    .data              inited   0x2000'0c95       0x1  main.o [7]
    .data              inited   0x2000'0c96       0x1  xiangzi.o [1]
    .data              inited   0x2000'0c97       0x1  xiangzi.o [1]
    .data              inited   0x2000'0c98       0x1  zf_common_debug.o [8]
    .data              inited   0x2000'0c99       0x1  zf_device_mt9v03x.o [10]
    .data              inited   0x2000'0c9a       0x1  zf_device_tft180.o [10]
    .data              inited   0x2000'0c9b       0x1  zf_device_tft180.o [10]
    .data              inited   0x2000'0c9c       0x1  zf_device_tft180.o [10]
    .data              inited   0x2000'0c9d       0x1  zf_device_tft180.o [10]
                              - 0x2000'0c9e     0xc9e

"P3-P5|P9", part 2 of 2:                     0x2'99a0
  ZI                            0x2000'0ca0  0x2'038c  <Block>
    .bss               zero     0x2000'0ca0       0x4  ce.o [1]
    .bss               zero     0x2000'0ca4       0x4  ce.o [1]
    .bss               zero     0x2000'0ca8       0xc  ce.o [1]
    .bss               zero     0x2000'0cb4       0xc  ce.o [1]
    .bss               zero     0x2000'0cc0       0x4  ce.o [1]
    .bss               zero     0x2000'0cc4       0x4  ce.o [1]
    .bss               zero     0x2000'0cc8       0x4  ce.o [1]
    .bss               zero     0x2000'0ccc       0x4  ce.o [1]
    .bss               zero     0x2000'0cd0       0x4  fsl_clock.o [6]
    .bss               zero     0x2000'0cd4       0x4  fsl_clock.o [6]
    .bss               zero     0x2000'0cd8       0x4  fsl_csi.o [6]
    .bss               zero     0x2000'0cdc       0x4  fsl_csi.o [6]
    .bss               zero     0x2000'0ce0      0x14  fsl_debug_console.o [5]
    .bss               zero     0x2000'0cf4      0x80  fsl_edma.o [6]
    .bss               zero     0x2000'0d74       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0d7c       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0d84       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0d8c       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0d94       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0d9c       0x8  fsl_enet.o [6]
    .bss               zero     0x2000'0da4      0x10  fsl_flexcan.o [6]
    .bss               zero     0x2000'0db4       0x4  fsl_flexcan.o [6]
    .bss               zero     0x2000'0db8       0x8  fsl_flexio.o [6]
    .bss               zero     0x2000'0dc0       0x8  fsl_flexio.o [6]
    .bss               zero     0x2000'0dc8       0x8  fsl_flexio.o [6]
    .bss               zero     0x2000'0dd0       0x4  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0dd4      0x14  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0de8       0x4  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0dec      0x14  fsl_lpi2c.o [6]
    .bss               zero     0x2000'0e00      0x14  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e14       0x4  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e18       0x4  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e1c       0x8  fsl_lpspi.o [6]
    .bss               zero     0x2000'0e24      0x24  fsl_lpuart.o [6]
    .bss               zero     0x2000'0e48       0x4  fsl_lpuart.o [6]
    .bss               zero     0x2000'0e4c      0x20  fsl_sai.o [6]
    .bss               zero     0x2000'0e6c       0x4  fsl_sai.o [6]
    .bss               zero     0x2000'0e70       0x4  fsl_sai.o [6]
    .bss               zero     0x2000'0e74       0x8  fsl_spdif.o [6]
    .bss               zero     0x2000'0e7c       0x4  fsl_spdif.o [6]
    .bss               zero     0x2000'0e80       0x4  fsl_spdif.o [6]
    .bss               zero     0x2000'0e84       0xc  fsl_usdhc.o [6]
    .bss               zero     0x2000'0e90       0x4  fsl_usdhc.o [6]
    .bss               zero     0x2000'0e94       0x4  isr.o [7]
    .bss               zero     0x2000'0e98       0x4  isr.o [7]
    .bss               zero     0x2000'0e9c       0x4  isr.o [7]
    .bss               zero     0x2000'0ea0       0x4  main.o [7]
    .bss               zero     0x2000'0ea4       0x4  main.o [7]
    .bss               zero     0x2000'0ea8       0x4  menu.o [1]
    .bss               zero     0x2000'0eac      0x10  menu.o [1]
    .bss               zero     0x2000'0ebc      0x10  menu.o [1]
    .bss               zero     0x2000'0ecc      0x10  menu.o [1]
    .bss               zero     0x2000'0edc       0x4  menu.o [1]
    .bss               zero     0x2000'0ee0       0x4  menu.o [1]
    .bss               zero     0x2000'0ee4       0x4  menu.o [1]
    .bss               zero     0x2000'0ee8       0x4  menu.o [1]
    .bss               zero     0x2000'0eec       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0ef0       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0ef4       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0ef8       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0efc       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f00       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f04       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f08       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f0c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f10       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f14       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f18       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f1c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f20       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f24       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f28       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f2c       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f30       0x4  siyuanshu.o [1]
    .bss               zero     0x2000'0f34  0x1'2c00  sxt.o [1]
    .bss               zero     0x2001'3b34    0x4b00  sxt.o [1]
    .bss               zero     0x2001'8634    0x4b00  sxt.o [1]
    .bss               zero     0x2001'd134     0x1e0  sxt.o [1]
    .bss               zero     0x2001'd314     0x1e0  sxt.o [1]
    .bss               zero     0x2001'd4f4     0x1e0  sxt.o [1]
    .bss               zero     0x2001'd6d4     0xfa0  sxt.o [1]
    .bss               zero     0x2001'e674     0xfa0  sxt.o [1]
    .bss               zero     0x2001'f614     0xfa0  sxt.o [1]
    .bss               zero     0x2002'05b4       0x4  sxt.o [1]
    .bss               zero     0x2002'05b8       0x4  sxt.o [1]
    .bss               zero     0x2002'05bc       0x4  sxt.o [1]
    .bss               zero     0x2002'05c0       0x4  sxt.o [1]
    .bss               zero     0x2002'05c4       0x4  sxt.o [1]
    .bss               zero     0x2002'05c8     0x190  sxt.o [1]
    .bss               zero     0x2002'0758     0x190  sxt.o [1]
    .bss               zero     0x2002'08e8     0x1e0  sxt.o [1]
    .bss               zero     0x2002'0ac8     0x1e0  sxt.o [1]
    .bss               zero     0x2002'0ca8       0x4  sxt.o [1]
    .bss               zero     0x2002'0cac       0x4  sxt.o [1]
    .bss               zero     0x2002'0cb0       0x4  sxt.o [1]
    .bss               zero     0x2002'0cb4       0x4  sxt.o [1]
    .bss               zero     0x2002'0cb8       0x4  sxt.o [1]
    .bss               zero     0x2002'0cbc       0x4  sxt.o [1]
    .bss               zero     0x2002'0cc0       0x4  sxt.o [1]
    .bss               zero     0x2002'0cc4       0x4  sxt.o [1]
    .bss               zero     0x2002'0cc8       0x4  sxt.o [1]
    .bss               zero     0x2002'0ccc       0x4  sxt.o [1]
    .bss               zero     0x2002'0cd0       0x4  sxt.o [1]
    .bss               zero     0x2002'0cd4       0x4  sxt.o [1]
    .bss               zero     0x2002'0cd8       0x4  uart.o [1]
    .bss               zero     0x2002'0cdc       0x8  uart.o [1]
    .bss               zero     0x2002'0ce4       0x4  uart.o [1]
    .bss               zero     0x2002'0ce8       0x4  uart.o [1]
    .bss               zero     0x2002'0cec       0x4  uart.o [1]
    .bss               zero     0x2002'0cf0       0x4  uart.o [1]
    .bss               zero     0x2002'0cf4       0x8  uart.o [1]
    .bss               zero     0x2002'0cfc       0x4  uart.o [1]
    .bss               zero     0x2002'0d00       0x8  uart.o [1]
    .bss               zero     0x2002'0d08       0x4  uart.o [1]
    .bss               zero     0x2002'0d0c       0x4  uart.o [1]
    .bss               zero     0x2002'0d10       0x4  uart.o [1]
    .bss               zero     0x2002'0d14       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d18       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d1c       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d20       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d24       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d28       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d2c       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d30       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0d34     0x12c  xiangzi.o [1]
    .bss               zero     0x2002'0e60       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e64       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e68       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e6c       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e70       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e74       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e78       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e7c       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e80       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e84       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e88       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e8c       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e90       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e94       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e98       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0e9c       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0ea0       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0ea4       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0ea8       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0eac       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0eb0       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0eb4       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0eb8       0x4  xiangzi.o [1]
    .bss               zero     0x2002'0ebc       0x4  yuansu.o [1]
    .bss               zero     0x2002'0ec0       0x4  yuansu.o [1]
    .bss               zero     0x2002'0ec4      0x40  zf_common_debug.o [8]
    .bss               zero     0x2002'0f04      0x18  zf_common_debug.o [8]
    .bss               zero     0x2002'0f1c      0x14  zf_common_debug.o [8]
    .bss               zero     0x2002'0f30       0x4  zf_common_interrupt.o [8]
    .bss               zero     0x2002'0f34      0x18  zf_device_camera.o [10]
    .bss               zero     0x2002'0f4c       0x8  zf_device_camera.o [10]
    .bss               zero     0x2002'0f54       0x4  zf_device_mt9v03x.o [10]
    .bss               zero     0x2002'0f58      0x38  zf_driver_csi.o [11]
    .bss               zero     0x2002'0f90       0xc  zf_driver_spi.o [11]
    .bss               zero     0x2002'0f9c      0x10  zf_driver_usb_cdc.o [11]
    .bss               zero     0x2002'0fac       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2002'0fb0       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2002'0fb4       0x4  zhuangtaiji.o [1]
    .bss               zero     0x2002'0fb8      0x14  zf_device_config.o [16]
    .bss               zero     0x2002'0fcc      0x10  zf_device_config.o [16]
    .bss               zero     0x2002'0fdc       0x4  zf_device_config.o [16]
    .bss               zero     0x2002'0fe0       0x4  zf_device_config.o [16]
    .bss               zero     0x2002'0fe4       0x8  heap0.o [12]
    .bss               zero     0x2002'0fec       0x4  xgetmemchunk.o [12]
    .bss               zero     0x2002'0ff0       0x2  sxt.o [1]
    .bss               zero     0x2002'0ff2       0x2  sxt.o [1]
    .bss               zero     0x2002'0ff4       0x2  sxt.o [1]
    .bss               zero     0x2002'0ff6       0x2  sxt.o [1]
    .bss               zero     0x2002'0ff8       0x2  sxt.o [1]
    .bss               zero     0x2002'0ffa       0x2  sxt.o [1]
    .bss               zero     0x2002'0ffc       0x2  sxt.o [1]
    .bss               zero     0x2002'0ffe       0x2  sxt.o [1]
    .bss               zero     0x2002'1000       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2002'1002       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2002'1004       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2002'1006       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2002'1008       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2002'100a       0x2  zf_device_imu660ra.o [10]
    .bss               zero     0x2002'100c       0x2  zf_device_mt9v03x.o [10]
    .bss               zero     0x2002'100e       0x2  zf_device_tft180.o [10]
    .bss               zero     0x2002'1010       0x1  isr.o [7]
    .bss               zero     0x2002'1011       0x1  isr.o [7]
    .bss               zero     0x2002'1012       0x1  main.o [7]
    .bss               zero     0x2002'1013       0x1  sxt.o [1]
    .bss               zero     0x2002'1014       0x1  uart.o [1]
    .bss               zero     0x2002'1015       0x1  uart.o [1]
    .bss               zero     0x2002'1016       0x1  uart.o [1]
    .bss               zero     0x2002'1017       0x1  uart.o [1]
    .bss               zero     0x2002'1018       0x1  uart.o [1]
    .bss               zero     0x2002'1019       0x1  uart.o [1]
    .bss               zero     0x2002'101a       0x1  xiangzi.o [1]
    .bss               zero     0x2002'101b       0x1  xiangzi.o [1]
    .bss               zero     0x2002'101c       0x1  xiangzi.o [1]
    .bss               zero     0x2002'101d       0x1  xiangzi.o [1]
    .bss               zero     0x2002'101e       0x1  xiangzi.o [1]
    .bss               zero     0x2002'101f       0x1  xiangzi.o [1]
    .bss               zero     0x2002'1020       0x1  xiangzi.o [1]
    .bss               zero     0x2002'1021       0x1  xiangzi.o [1]
    .bss               zero     0x2002'1022       0x1  xiangzi.o [1]
    .bss               zero     0x2002'1023       0x1  zf_common_debug.o [8]
    .bss               zero     0x2002'1024       0x1  zf_common_debug.o [8]
    .bss               zero     0x2002'1025       0x1  zf_common_debug.o [8]
    .bss               zero     0x2002'1026       0x1  zf_device_mt9v03x.o [10]
    .bss               zero     0x2002'1027       0x1  zf_device_type.o [10]
    .bss               zero     0x2002'1028       0x1  zf_driver_pit.o [11]
  DTCM_VAR                      0x2002'1040    0x9610  <Block>
    NonCacheable       zero     0x2002'1040    0x4b00  zf_device_mt9v03x.o [10]
    NonCacheable       zero     0x2002'5b40    0x4b00  zf_device_mt9v03x.o [10]
                              - 0x2002'a640  0x2'99a0

"P6":                                           0x400
  HEAP                          0x2002'a640     0x400  <Block>
    HEAP               uninit   0x2002'a640     0x400  <Block tail>
                              - 0x2002'aa40     0x400

"P7":                                          0x1000
  CSTACK                        0x2006'f000    0x1000  <Block>
    CSTACK             uninit   0x2006'f000    0x1000  <Block tail>
                              - 0x2007'0000    0x1000

"A2":                                           0x200
  .boot_hdr.conf       const    0x7000'0000     0x200  evkmimxrt1064_flexspi_nor_config.o [5]
                              - 0x7000'0200     0x200

"A3":                                            0x20
  .boot_hdr.ivt        const    0x7000'1000      0x20  fsl_flexspi_nor_boot.o [5]
                              - 0x7000'1020      0x20

"A4":                                            0x10
  .boot_hdr.boot_data  const    0x7000'1020      0x10  fsl_flexspi_nor_boot.o [5]
                              - 0x7000'1030      0x10

"A5":                                           0x410
  .boot_hdr.dcd_data   const    0x7000'1030     0x410  evkmimxrt1064_sdram_ini_dcd.o [5]
                              - 0x7000'1440     0x410

"A0":                                           0x440
  .intvec              const    0x7000'2000     0x400  zf_common_vector.o [8]
  .intvec              ro code  0x7000'2400      0x40  vector_table_M.o [14]
                              - 0x7000'2440     0x440

"P1":                                          0xeb6a
  .text                ro code  0x7000'2440     0x1f8  lz77_init.o [14]
  .text                ro code  0x7000'2638       0x6  abort.o [12]
  .text                ro code  0x7000'2640      0x14  exit.o [15]
  .text                ro code  0x7000'2654     0x124  system_MIMXRT1064.o [5]
  .text                ro code  0x7000'2778       0x2  system_MIMXRT1064.o [5]
  .text                ro code  0x7000'277c      0x6c  startup_MIMXRT1064.o [5]
  .text                ro code  0x7000'27e8      0x38  zero_init3.o [14]
  .text                ro code  0x7000'2820      0x28  data_init.o [14]
  .text                ro code  0x7000'2848      0x22  fpinit_M.o [13]
  .iar.init_table      const    0x7000'286c      0x50  - Linker created -
  Veneer               ro code  0x7000'28bc       0x8  - Linker created -
  Veneer               ro code  0x7000'28c4       0x8  - Linker created -
  .text                ro code  0x7000'28cc      0x1e  cmain.o [14]
  .text                ro code  0x7000'28ea       0x4  low_level_init.o [12]
  .text                ro code  0x7000'28f0      0x1c  cstartup_M.o [14]
  .rodata              const    0x7000'290c       0x0  zero_init3.o [14]
  .rodata              const    0x7000'290c       0x0  lz77_init.o [14]
  Initializer bytes    const    0x7000'290c    0xe3af  <for rw-1>
  Initializer bytes    const    0x7001'0cbb     0x2ef  <for P3-P5|P9 s0>
                              - 0x7001'0faa    0xeb6a

"P2|P11":                                    0x1'5a40
  RW                            0x8000'0400  0x1'5a40  <Block>
    rw-1                        0x8000'0400  0x1'5a40  <Init block>
      .rodata          inited   0x8000'0400      0x48  sxt.o [1]
      .rodata          inited   0x8000'0448     0x420  pow64.o [13]
      .text            inited   0x8000'0868      0x88  asin.o [13]
      .text            inited   0x8000'08f0     0x118  xatan.o [13]
      .rodata          inited   0x8000'0a08       0xc  ce.o [1]
      .rodata          inited   0x8000'0a14       0x4  ce.o [1]
      .rodata          inited   0x8000'0a18       0x4  ce.o [1]
      .rodata          inited   0x8000'0a1c       0x4  ce.o [1]
      .rodata          inited   0x8000'0a20      0x18  clock_config.o [5]
      .rodata          inited   0x8000'0a38      0x70  clock_config.o [5]
      .rodata          inited   0x8000'0aa8      0x10  clock_config.o [5]
      .rodata          inited   0x8000'0ab8       0x8  clock_config.o [5]
      .rodata          inited   0x8000'0ac0      0x2c  fsl_assert.o [5]
      .rodata          inited   0x8000'0aec      0x10  fsl_clock.o [6]
      .rodata          inited   0x8000'0afc      0x70  fsl_clock.o [6]
      .rodata          inited   0x8000'0b6c      0x10  fsl_clock.o [6]
      .rodata          inited   0x8000'0b7c      0x10  fsl_csi.o [6]
      .rodata          inited   0x8000'0b8c      0x70  fsl_csi.o [6]
      .rodata          inited   0x8000'0bfc      0x3c  fsl_csi.o [6]
      .rodata          inited   0x8000'0c38      0x70  fsl_csi.o [6]
      .rodata          inited   0x8000'0ca8       0xc  fsl_csi.o [6]
      .rodata          inited   0x8000'0cb4       0xc  fsl_csi.o [6]
      .rodata          inited   0x8000'0cc0       0x4  fsl_csi.o [6]
      .rodata          inited   0x8000'0cc4      0x70  fsl_edma.o [6]
      .rodata          inited   0x8000'0d34      0x1c  fsl_edma.o [6]
      .rodata          inited   0x8000'0d50       0xc  fsl_edma.o [6]
      .rodata          inited   0x8000'0d5c      0x3c  fsl_enet.o [6]
      .rodata          inited   0x8000'0d98      0x70  fsl_enet.o [6]
      .rodata          inited   0x8000'0e08       0x8  fsl_enet.o [6]
      .rodata          inited   0x8000'0e10      0x74  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0e84      0x18  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0e9c      0x18  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0eb4      0x18  fsl_flexcan.o [6]
      .rodata          inited   0x8000'0ecc      0x10  fsl_gpio.o [6]
      .rodata          inited   0x8000'0edc      0x70  fsl_gpio.o [6]
      .rodata          inited   0x8000'0f4c      0x3c  fsl_gpio.o [6]
      .rodata          inited   0x8000'0f88      0x70  fsl_gpio.o [6]
      .rodata          inited   0x8000'0ff8       0xc  fsl_gpio.o [6]
      .rodata          inited   0x8000'1004      0x2c  fsl_gpio.o [6]
      .rodata          inited   0x8000'1030       0xc  fsl_gpio.o [6]
      .rodata          inited   0x8000'103c      0x10  fsl_gpt.o [6]
      .rodata          inited   0x8000'104c      0x70  fsl_gpt.o [6]
      .rodata          inited   0x8000'10bc      0x1c  fsl_gpt.o [6]
      .rodata          inited   0x8000'10d8      0x70  fsl_gpt.o [6]
      .rodata          inited   0x8000'1148      0x3c  fsl_gpt.o [6]
      .rodata          inited   0x8000'1184      0x70  fsl_gpt.o [6]
      .rodata          inited   0x8000'11f4      0x10  fsl_gpt.o [6]
      .rodata          inited   0x8000'1204       0xc  fsl_gpt.o [6]
      .rodata          inited   0x8000'1210       0xc  fsl_gpt.o [6]
      .rodata          inited   0x8000'121c       0x8  fsl_gpt.o [6]
      .rodata          inited   0x8000'1224      0x10  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1234      0x70  fsl_lpspi.o [6]
      .rodata          inited   0x8000'12a4      0x40  fsl_lpspi.o [6]
      .rodata          inited   0x8000'12e4      0x70  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1354      0x14  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1368      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1380      0x10  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1390       0xc  fsl_lpspi.o [6]
      .rodata          inited   0x8000'139c       0xc  fsl_lpspi.o [6]
      .rodata          inited   0x8000'13a8      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'13c0      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'13d8      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'13f0      0x18  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1408       0x8  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1410      0x14  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1424       0xc  fsl_lpspi.o [6]
      .rodata          inited   0x8000'1430      0x10  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1440      0x70  fsl_lpuart.o [6]
      .rodata          inited   0x8000'14b0      0x40  fsl_lpuart.o [6]
      .rodata          inited   0x8000'14f0      0x70  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1560       0xc  fsl_lpuart.o [6]
      .rodata          inited   0x8000'156c       0xc  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1578      0x1c  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1594      0x28  fsl_lpuart.o [6]
      .rodata          inited   0x8000'15bc      0x28  fsl_lpuart.o [6]
      .rodata          inited   0x8000'15e4      0x24  fsl_lpuart.o [6]
      .rodata          inited   0x8000'1608      0x14  fsl_lpuart.o [6]
      .rodata          inited   0x8000'161c      0x10  fsl_pit.o [6]
      .rodata          inited   0x8000'162c      0x70  fsl_pit.o [6]
      .rodata          inited   0x8000'169c      0x3c  fsl_pit.o [6]
      .rodata          inited   0x8000'16d8      0x70  fsl_pit.o [6]
      .rodata          inited   0x8000'1748       0xc  fsl_pit.o [6]
      .rodata          inited   0x8000'1754       0x4  fsl_pit.o [6]
      .rodata          inited   0x8000'1758      0x10  fsl_pwm.o [6]
      .rodata          inited   0x8000'1768      0x70  fsl_pwm.o [6]
      .rodata          inited   0x8000'17d8      0x3c  fsl_pwm.o [6]
      .rodata          inited   0x8000'1814      0x70  fsl_pwm.o [6]
      .rodata          inited   0x8000'1884       0x8  fsl_pwm.o [6]
      .rodata          inited   0x8000'188c       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'1898       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'18a4       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'18b0       0xc  fsl_pwm.o [6]
      .rodata          inited   0x8000'18bc      0x1c  fsl_pwm.o [6]
      .rodata          inited   0x8000'18d8      0x14  fsl_pwm.o [6]
      .rodata          inited   0x8000'18ec      0x28  fsl_pwm.o [6]
      .rodata          inited   0x8000'1914      0x10  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1924      0x70  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1994      0x3c  fsl_qtmr.o [6]
      .rodata          inited   0x8000'19d0      0x70  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1a40       0xc  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1a4c      0x14  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1a60       0xc  fsl_qtmr.o [6]
      .rodata          inited   0x8000'1a6c      0x70  fsl_sai.o [6]
      .rodata          inited   0x8000'1adc      0x18  fsl_sai.o [6]
      .rodata          inited   0x8000'1af4      0x18  fsl_sai.o [6]
      .rodata          inited   0x8000'1b0c       0xc  fsl_usdhc.o [6]
      .rodata          inited   0x8000'1b18     0x500  hanzi.o [1]
      .rodata          inited   0x8000'2018      0x20  hanzi.o [1]
      .rodata          inited   0x8000'2038       0xc  menu.o [1]
      .rodata          inited   0x8000'2044       0x4  menu.o [1]
      .rodata          inited   0x8000'2048       0x8  menu.o [1]
      .rodata          inited   0x8000'2050       0x8  menu.o [1]
      .rodata          inited   0x8000'2058       0x8  menu.o [1]
      .rodata          inited   0x8000'2060       0x8  menu.o [1]
      .rodata          inited   0x8000'2068       0x8  menu.o [1]
      .rodata          inited   0x8000'2070       0x8  menu.o [1]
      .rodata          inited   0x8000'2078       0x8  menu.o [1]
      .rodata          inited   0x8000'2080       0x8  menu.o [1]
      .rodata          inited   0x8000'2088       0x8  menu.o [1]
      .rodata          inited   0x8000'2090       0x8  menu.o [1]
      .rodata          inited   0x8000'2098       0x4  menu.o [1]
      .rodata          inited   0x8000'209c       0x4  menu.o [1]
      .rodata          inited   0x8000'20a0       0x4  menu.o [1]
      .rodata          inited   0x8000'20a4       0x4  menu.o [1]
      .rodata          inited   0x8000'20a8       0x8  menu.o [1]
      .rodata          inited   0x8000'20b0       0x8  menu.o [1]
      .rodata          inited   0x8000'20b8       0x8  menu.o [1]
      .rodata          inited   0x8000'20c0       0x4  menu.o [1]
      .rodata          inited   0x8000'20c4       0x4  menu.o [1]
      .rodata          inited   0x8000'20c8       0x4  menu.o [1]
      .rodata          inited   0x8000'20cc       0xc  menu.o [1]
      .rodata          inited   0x8000'20d8       0xc  menu.o [1]
      .rodata          inited   0x8000'20e4       0xc  menu.o [1]
      .rodata          inited   0x8000'20f0       0x8  menu.o [1]
      .rodata          inited   0x8000'20f8       0x8  menu.o [1]
      .rodata          inited   0x8000'2100       0xc  menu.o [1]
      .rodata          inited   0x8000'210c       0xc  menu.o [1]
      .rodata          inited   0x8000'2118       0x4  menu.o [1]
      .rodata          inited   0x8000'211c       0x4  menu.o [1]
      .rodata          inited   0x8000'2120       0x4  menu.o [1]
      .rodata          inited   0x8000'2124       0x4  menu.o [1]
      .rodata          inited   0x8000'2128       0x4  menu.o [1]
      .rodata          inited   0x8000'212c       0x4  menu.o [1]
      .rodata          inited   0x8000'2130       0xc  zf_common_debug.o [8]
      .rodata          inited   0x8000'213c      0x10  zf_common_debug.o [8]
      .rodata          inited   0x8000'214c       0xc  zf_common_debug.o [8]
      .rodata          inited   0x8000'2158      0x1c  zf_common_debug.o [8]
      .rodata          inited   0x8000'2174      0x18  zf_common_debug.o [8]
      .rodata          inited   0x8000'218c      0x10  zf_common_debug.o [8]
      .rodata          inited   0x8000'219c       0xc  zf_common_debug.o [8]
      .rodata          inited   0x8000'21a8      0x74  zf_common_fifo.o [8]
      .rodata          inited   0x8000'221c     0x5f0  zf_common_font.o [8]
      .rodata          inited   0x8000'280c     0x228  zf_common_font.o [8]
      .rodata          inited   0x8000'2a34      0x78  zf_common_function.o [8]
      .rodata          inited   0x8000'2aac      0x1c  zf_device_imu660ra.o [10]
      .rodata          inited   0x8000'2ac8      0x78  zf_device_imu660ra.o [10]
      .rodata          inited   0x8000'2b40      0x18  zf_device_imu660ra.o [10]
      .rodata          inited   0x8000'2b58      0x1c  zf_device_mt9v03x.o [10]
      .rodata          inited   0x8000'2b74      0x78  zf_device_mt9v03x.o [10]
      .rodata          inited   0x8000'2bec      0x1c  zf_device_mt9v03x.o [10]
      .rodata          inited   0x8000'2c08      0x78  zf_device_tft180.o [10]
      .rodata          inited   0x8000'2c80      0x18  zf_driver_csi.o [11]
      .rodata          inited   0x8000'2c98      0x70  zf_driver_csi.o [11]
      .rodata          inited   0x8000'2d08      0x74  zf_driver_csi.o [11]
      .rodata          inited   0x8000'2d7c      0x28  zf_driver_delay.o [11]
      .rodata          inited   0x8000'2da4      0x70  zf_driver_delay.o [11]
      .rodata          inited   0x8000'2e14      0x78  zf_driver_encoder.o [11]
      .rodata          inited   0x8000'2e8c       0xc  zf_driver_gpio.o [11]
      .rodata          inited   0x8000'2e98      0x70  zf_driver_gpio.o [11]
      .rodata          inited   0x8000'2f08       0xc  zf_driver_pit.o [11]
      .rodata          inited   0x8000'2f14      0x70  zf_driver_pit.o [11]
      .rodata          inited   0x8000'2f84       0xc  zf_driver_pit.o [11]
      .rodata          inited   0x8000'2f90      0x74  zf_driver_pwm.o [11]
      .rodata          inited   0x8000'3004      0x78  zf_driver_soft_iic.o [11]
      .rodata          inited   0x8000'307c      0x18  zf_driver_spi.o [11]
      .rodata          inited   0x8000'3094      0x70  zf_driver_spi.o [11]
      .rodata          inited   0x8000'3104      0x74  zf_driver_spi.o [11]
      .rodata          inited   0x8000'3178      0x74  zf_driver_uart.o [11]
      .rodata          inited   0x8000'31ec    0x2000  zf_device_config.o [16]
      .text            inited   0x8000'51ec     0x2ac  board.o [5]
      .text            inited   0x8000'5498     0x4bc  ce.o [1]
      .text            inited   0x8000'5954     0x6f8  clock_config.o [5]
      .text            inited   0x8000'604c      0x1c  fsl_assert.o [5]
      .text            inited   0x8000'6068     0x820  fsl_clock.o [6]
      .text            inited   0x8000'6888     0x668  fsl_csi.o [6]
      .text            inited   0x8000'6ef0     0xb2c  fsl_debug_console.o [5]
      .text            inited   0x8000'7a1c     0x4d0  fsl_edma.o [6]
      .text            inited   0x8000'7eec     0x150  fsl_enet.o [6]
      .text            inited   0x8000'803c      0xa4  fsl_flexcan.o [6]
      .text            inited   0x8000'80e0      0x68  fsl_flexio.o [6]
      .text            inited   0x8000'8148     0x216  fsl_gpio.o [6]
      .text            inited   0x8000'8360     0x24c  fsl_gpt.o [6]
      .text            inited   0x8000'85ac      0x94  fsl_lpi2c.o [6]
      .text            inited   0x8000'8640     0xc38  fsl_lpspi.o [6]
      .text            inited   0x8000'9278     0x55c  fsl_lpuart.o [6]
      .text            inited   0x8000'97d4     0x164  fsl_pit.o [6]
      .text            inited   0x8000'9938     0xb28  fsl_pwm.o [6]
      .text            inited   0x8000'a460     0x1e4  fsl_qtmr.o [6]
      .text            inited   0x8000'a644     0x148  fsl_sai.o [6]
      .text            inited   0x8000'a78c      0x48  fsl_spdif.o [6]
      .text            inited   0x8000'a7d4      0x38  fsl_usdhc.o [6]
      .text            inited   0x8000'a80c     0x4d0  isr.o [7]
      .text            inited   0x8000'acdc     0x5f0  main.o [7]
      .text            inited   0x8000'b2cc     0x660  menu.o [1]
      .text            inited   0x8000'b92c     0x6fc  siyuanshu.o [1]
      .text            inited   0x8000'c028    0x1164  sxt.o [1]
      .text            inited   0x8000'd18c     0x540  uart.o [1]
      .text            inited   0x8000'd6cc     0x176  usb_device_dci.o [4]
      .text            inited   0x8000'd844     0x5d0  usb_device_ehci.o [4]
      .text            inited   0x8000'de14     0xe08  xiangzi.o [1]
      .text            inited   0x8000'ec1c      0x20  zf_common_clock.o [8]
      .text            inited   0x8000'ec3c     0x54c  zf_common_debug.o [8]
      .text            inited   0x8000'f188     0x624  zf_common_fifo.o [8]
      .text            inited   0x8000'f7ac     0x230  zf_common_function.o [8]
      .text            inited   0x8000'f9dc      0xec  zf_common_interrupt.o [8]
      .text            inited   0x8000'fac8      0x1c  zf_device_camera.o [10]
      .text            inited   0x8000'fae4     0x2bc  zf_device_imu660ra.o [10]
      .text            inited   0x8000'fda0     0x3b8  zf_device_mt9v03x.o [10]
      .text            inited   0x8001'0158     0xf5c  zf_device_tft180.o [10]
      .text            inited   0x8001'10b4      0x2c  zf_device_type.o [10]
      .text            inited   0x8001'10e0     0x478  zf_driver_csi.o [11]
      .text            inited   0x8001'1558      0xc8  zf_driver_delay.o [11]
      .text            inited   0x8001'1620     0x5be  zf_driver_encoder.o [11]
      .text            inited   0x8001'1be0     0x2e4  zf_driver_gpio.o [11]
      .text            inited   0x8001'1ec4     0x190  zf_driver_pit.o [11]
      .text            inited   0x8001'2054     0x90c  zf_driver_pwm.o [11]
      .text            inited   0x8001'2960     0x658  zf_driver_soft_iic.o [11]
      .text            inited   0x8001'2fb8     0xafc  zf_driver_spi.o [11]
      .text            inited   0x8001'3ab4     0x5cc  zf_driver_uart.o [11]
      .text            inited   0x8001'4080      0x20  zf_driver_usb_cdc.o [11]
      .text            inited   0x8001'40a0     0x6ac  zf_device_config.o [16]
      .text            inited   0x8001'474c      0x1e  sqrt.o [13]
      .text            inited   0x8001'476c      0xa6  ABImemcpy.o [14]
      .text            inited   0x8001'4814      0x66  ABImemset.o [14]
      .text            inited   0x8001'487c     0x274  I64DivMod.o [14]
      .text            inited   0x8001'4af0      0x66  DblToS64.o [13]
      .text            inited   0x8001'4b58      0x54  S64ToDbl.o [13]
      .text            inited   0x8001'4bac      0x9e  modf.o [13]
      .text            inited   0x8001'4c4c     0x510  pow64.o [13]
      .text            inited   0x8001'515c      0x36  strlen.o [14]
      .text            inited   0x8001'5194      0x12  strcmp.o [14]
      .text            inited   0x8001'51a8      0x7e  atan2.o [13]
      .text            inited   0x8001'5228      0x6a  atan2f.o [13]
      .text            inited   0x8001'5294      0x40  sprintf.o [12]
      .text            inited   0x8001'52d4     0x13c  heap0.o [12]
      .text            inited   0x8001'5410       0x2  I64DivZer.o [14]
      .text            inited   0x8001'5414     0x2c0  iar_Exp64.o [13]
      .text            inited   0x8001'56d4      0x7c  frexp.o [13]
      .text            inited   0x8001'5750      0xf0  ldexp.o [13]
      .text            inited   0x8001'5840      0xa4  xatanf.o [13]
      .text            inited   0x8001'58e4      0x2c  xgetmemchunk.o [12]
      Veneer           inited   0x8001'5910       0x8  - Linker created -
      .text            inited   0x8001'5918       0xa  cexit.o [14]
      .rodata          inited   0x8001'5922       0x2  fsl_csi.o [6]
      .rodata          inited   0x8001'5924       0x2  fsl_csi.o [6]
      .rodata          inited   0x8001'5926       0x2  fsl_pit.o [6]
      .rodata          inited   0x8001'5928       0x2  menu.o [1]
      .text            inited   0x8001'592a     0x168  init.o [1]
      .text            inited   0x8001'5a92      0x10  zf_common_vector.o [8]
      .text            inited   0x8001'5aa2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5aa4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5aac       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5ab4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5abc       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5ac4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5acc       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5ad4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5adc       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5ae4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5aec       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5af4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5afc       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b04       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b0c       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b14       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b1c       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b24       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b2c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b2e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b30       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b32       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b3a       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b42       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b4a       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b52       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b5a       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b62       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b6a       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b72       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b7a       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b82       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5b8a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b8c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b8e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b90       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b92       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b94       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b96       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b98       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b9a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b9c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5b9e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ba0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ba2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ba4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ba6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ba8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5baa       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bac       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5bb4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5bbc       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5bc4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5bcc       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5bd4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bd6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bd8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bda       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bdc       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bde       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5be0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5be2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5be4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5be6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5be8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bea       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bec       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bee       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bf0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bf2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bf4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bf6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bf8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bfa       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bfc       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5bfe       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c00       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c02       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c04       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c0c       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c14       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c16       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c18       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c1a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c1c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c1e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c20       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c22       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c24       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c26       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c28       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c2a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c2c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c2e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c30       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c32       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c3a       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c42       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c44       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c4c       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c54       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c5c       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5c64       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c66       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c68       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c6a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c6c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c6e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c70       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c72       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c74       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c76       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c78       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c7a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c7c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c7e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c80       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c82       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c84       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c86       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c88       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c8a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c8c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c8e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c90       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c92       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c94       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c96       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c98       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c9a       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c9c       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5c9e       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ca0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ca2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ca4       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ca6       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5ca8       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5caa       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5cb2       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5cba       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5cc2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5cc4       0x8  zf_common_vector.o [8]
      .text            inited   0x8001'5ccc       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5cce       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5cd0       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5cd2       0x2  zf_common_vector.o [8]
      .text            inited   0x8001'5cd4       0x4  heaptramp0.o [12]
      Veneer           inited   0x8001'5cd8       0x8  - Linker created -
      .text            inited   0x8001'5ce0      0x16  vla_alloc.o [12]
      .text            inited   0x8001'5cf6       0xa  xsprout.o [12]
      .text            inited   0x8001'5d00       0x4  exit.o [12]
      .text            inited   0x8001'5d04     0x13c  xprintftiny.o [12]
                              - 0x8001'5e40  0x1'5a40

Unused ranges:

         From           To        Size
         ----           --        ----
  0x2000'0c9e  0x2000'0c9f         0x2
  0x2002'aa40  0x2006'efff    0x4'45c0
  0x7001'0faa  0x703f'ffff   0x3e'f056
  0x8000'0000  0x8000'03ff       0x400
  0x8001'5e40  0x81df'ffff  0x1de'a1c0


*******************************************************************************
*** INIT TABLE
***

          Address      Size
          -------      ----
Zero (__iar_zero_init3)
    2 destination ranges, total size 0x2'9989:
          0x2000'0ca0  0x2'0389
          0x2002'1040    0x9600

Copy/lz77 (__iar_lz77_init3)
    1 source range, total size 0xe3af (65% of destination):
          0x7000'290c    0xe3af
    1 destination range, total size 0x1'5a40:
          0x8000'0400  0x1'5a40

Copy/lz77 (__iar_lz77_init3)
    1 source range, total size 0x2ef (23% of destination):
          0x7001'0cbb     0x2ef
    1 destination range, total size 0xc9e:
          0x2000'0000     0xc9e



*******************************************************************************
*** MODULE SUMMARY
***

    Module                              ro code  rw code  ro data  rw data
    ------                              -------  -------  -------  -------
command line/config:
    ----------------------------------------------------------------------
    Total:

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir: [1]
    ce.o                                           1'212      852      240
    hanzi.o                                                   863    1'312
    init.o                                           360      237
    menu.o                                         1'632    1'824    2'842
    siyuanshu.o                                    1'788    1'178       84
    sxt.o                                          4'452    2'983  130'593
    uart.o                                         1'344      884       66
    xiangzi.o                                      3'592    2'368      459
    yuansu.o                                                             8
    zhuangtaiji.o                                              46      208
    ----------------------------------------------------------------------
    Total:                                        14'380   11'235  135'812

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir: [2]
    ----------------------------------------------------------------------
    Total:

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir: [3]
    ----------------------------------------------------------------------
    Total:

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir: [4]
    usb_device_dci.o                                 374      246
    usb_device_ehci.o                              1'488      979
    ----------------------------------------------------------------------
    Total:                                         1'862    1'225

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir: [5]
    board.o                                          684      449
    clock_config.o                                 1'784    1'279      160
    evkmimxrt1064_flexspi_nor_config.o                        512
    evkmimxrt1064_sdram_ini_dcd.o                           1'040
    fsl_assert.o                                      28       47       44
    fsl_debug_console.o                            2'860    1'881       20
    fsl_flexspi_nor_boot.o                                     48
    startup_MIMXRT1064.o                    108
    system_MIMXRT1064.o                     294                 1        4
    ----------------------------------------------------------------------
    Total:                                  402    5'356    5'257      228

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir: [6]
    fsl_clock.o                                    2'080    1'462      152
    fsl_csi.o                                      1'640    1'297      340
    fsl_edma.o                                     1'232      910      280
    fsl_enet.o                                       336      339      228
    fsl_flexcan.o                                    164      232      208
    fsl_flexio.o                                     104       68       24
    fsl_gpio.o                                       534      593      368
    fsl_gpt.o                                        588      709      488
    fsl_lpi2c.o                                      148       98       48
    fsl_lpspi.o                                    3'128    2'400      560
    fsl_lpuart.o                                   1'372    1'227      532
    fsl_pit.o                                        356      443      318
    fsl_pwm.o                                      2'856    2'170      444
    fsl_qtmr.o                                       484      544      344
    fsl_sai.o                                        328      321      200
    fsl_spdif.o                                       72       47       16
    fsl_usdhc.o                                       56       45       28
    ----------------------------------------------------------------------
    Total:                                        15'478   12'905    4'578

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir: [7]
    isr.o                                          1'232      810       14
    main.o                                         1'520    1'001       17
    ----------------------------------------------------------------------
    Total:                                         2'752    1'811       31

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir: [8]
    zf_common_clock.o                                 32       22        4
    zf_common_debug.o                              1'356      971      232
    zf_common_fifo.o                               1'572    1'110      116
    zf_common_font.o                                        1'363    2'072
    zf_common_function.o                             560      446      120
    zf_common_interrupt.o                            236      155        4
    zf_common_vector.o                               578    1'404
    ----------------------------------------------------------------------
    Total:                                         4'334    5'471    2'548

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir: [9]
    ----------------------------------------------------------------------
    Total:

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir: [10]
    zf_device_camera.o                                28       19       32
    zf_device_imu660ra.o                             700      576      192
    zf_device_mt9v03x.o                              952      760   38'660
    zf_device_tft180.o                             3'932    2'666      128
    zf_device_type.o                                  44       33       21
    ----------------------------------------------------------------------
    Total:                                         5'656    4'054   39'033

C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir: [11]
    zf_driver_csi.o                                1'144      919      308
    zf_driver_delay.o                                200      231      152
    zf_driver_encoder.o                            1'470    1'050      140
    zf_driver_gpio.o                                 740      579      168
    zf_driver_pit.o                                  400      353      137
    zf_driver_pwm.o                                2'316    1'604      136
    zf_driver_soft_iic.o                           1'624    1'147      120
    zf_driver_spi.o                                2'812    2'020      284
    zf_driver_uart.o                               1'484    1'059      152
    zf_driver_usb_cdc.o                               32       21       16
    ----------------------------------------------------------------------
    Total:                                        12'222    8'983    1'613

dl7M_tlf.a: [12]
    abort.o                                   6
    exit.o                                             4        3
    heap0.o                                          316      208        8
    heaptramp0.o                                       4        3
    low_level_init.o                          4
    sprintf.o                                         64       44
    vla_alloc.o                                       22       15
    xgetmemchunk.o                                    44       29        4
    xprintftiny.o                                    316      208
    xsprout.o                                         10        6
    ----------------------------------------------------------------------
    Total:                                   10      780      516       12

m7M_tlv.a: [13]
    DblToS64.o                                       102       68
    S64ToDbl.o                                        84       56
    asin.o                                           136       90
    atan2.o                                          126       84
    atan2f.o                                         106       71
    fpinit_M.o                               34
    frexp.o                                          124       82
    iar_Exp64.o                                      704      464
    ldexp.o                                          240      157
    modf.o                                           158      104
    pow64.o                                        1'296    1'548    1'056
    sqrt.o                                            30       19
    xatan.o                                          280      184
    xatanf.o                                         164      108
    ----------------------------------------------------------------------
    Total:                                   34    3'550    3'035    1'056

rt7M_tl.a: [14]
    ABImemcpy.o                                      166      111
    ABImemset.o                                      102       68
    I64DivMod.o                                      628      414
    I64DivZer.o                                        2        1
    cexit.o                                           10        6
    cmain.o                                  30
    cstartup_M.o                             28
    data_init.o                              40
    lz77_init.o                             504
    strcmp.o                                          18       13
    strlen.o                                          54       35
    vector_table_M.o                         64
    zero_init3.o                             56
    ----------------------------------------------------------------------
    Total:                                  722      980      648

shb_l.a: [15]
    exit.o                                   20
    ----------------------------------------------------------------------
    Total:                                   20

zf_device_config.a: [16]
    zf_device_config.o                             1'708    6'511    8'236
    ----------------------------------------------------------------------
    Total:                                         1'708    6'511    8'236

    Gaps                                      6       26        2
    Linker created                           16       16       91    5'120
--------------------------------------------------------------------------
    Grand Total:                          1'210   69'100   61'744  198'267


*******************************************************************************
*** ENTRY LIST
***

Entry                       Address      Size  Type      Object
-----                       -------      ----  ----      ------
.iar.init_table$$Base   0x7000'286c             --   Gb  - Linker created -
.iar.init_table$$Limit  0x7000'28bc             --   Gb  - Linker created -
?main                   0x7000'28cd            Code  Gb  cmain.o [14]
ACMP1_IRQHandler        0x8001'5c71       0x2  Code  Wk  zf_common_vector.o [8]
ACMP2_IRQHandler        0x8001'5c73       0x2  Code  Wk  zf_common_vector.o [8]
ACMP3_IRQHandler        0x8001'5c75       0x2  Code  Wk  zf_common_vector.o [8]
ACMP4_IRQHandler        0x8001'5c77       0x2  Code  Wk  zf_common_vector.o [8]
ADC1_IRQHandler         0x8001'5be1       0x2  Code  Wk  zf_common_vector.o [8]
ADC2_IRQHandler         0x8001'5be3       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_ERROR_IRQ_IRQHandler
                        0x8001'5c6f       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_IRQ0_IRQHandler
                        0x8001'5c69       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_IRQ1_IRQHandler
                        0x8001'5c6b       0x2  Code  Wk  zf_common_vector.o [8]
ADC_ETC_IRQ2_IRQHandler
                        0x8001'5c6d       0x2  Code  Wk  zf_common_vector.o [8]
ARM_MPU_Disable         0x8000'5209      0x1a  Code  Lc  board.o [5]
ARM_MPU_Enable          0x8000'51ed      0x1c  Code  Lc  board.o [5]
Aldata                  0x2002'0fe4       0x8  Data  Lc  heap0.o [12]
Angle_ax                0x2000'0f28       0x4  Data  Gb  siyuanshu.o [1]
Angle_ay                0x2000'0f2c       0x4  Data  Gb  siyuanshu.o [1]
Angle_az                0x2000'0f30       0x4  Data  Gb  siyuanshu.o [1]
Angle_gx                0x2000'0f1c       0x4  Data  Gb  siyuanshu.o [1]
Angle_gy                0x2000'0f20       0x4  Data  Gb  siyuanshu.o [1]
Angle_gz                0x2000'0f24       0x4  Data  Gb  siyuanshu.o [1]
BEE_IRQHandler          0x8001'5bab       0x2  Code  Wk  zf_common_vector.o [8]
BOARD_BootClockRUN      0x8000'5ae5     0x43a  Code  Gb  clock_config.o [5]
BOARD_ConfigMPU         0x8000'52c3     0x154  Code  Gb  board.o [5]
BOARD_DebugConsoleSrcFreq
                        0x8000'5289      0x3a  Code  Gb  board.o [5]
BOARD_InitBootClocks    0x8000'5add       0x8  Code  Gb  clock_config.o [5]
BusFault_Handler        0x8001'5a99       0x2  Code  Gb  zf_common_vector.o [8]
CAN1_DriverIRQHandler   0x8000'803d      0x2a  Code  Gb  fsl_flexcan.o [6]
CAN1_IRQHandler         0x8001'5b7b       0x8  Code  Wk  zf_common_vector.o [8]
CAN2_DriverIRQHandler   0x8000'8067      0x2a  Code  Gb  fsl_flexcan.o [6]
CAN2_IRQHandler         0x8001'5b83       0x8  Code  Wk  zf_common_vector.o [8]
CAN3_DriverIRQHandler   0x8000'8091      0x2a  Code  Gb  fsl_flexcan.o [6]
CAN3_IRQHandler         0x8001'5cbb       0x8  Code  Wk  zf_common_vector.o [8]
CCM_1_IRQHandler        0x8001'5c1b       0x2  Code  Wk  zf_common_vector.o [8]
CCM_2_IRQHandler        0x8001'5c1d       0x2  Code  Wk  zf_common_vector.o [8]
CLOCK_ControlGate       0x8000'5a41      0x50  Code  Lc  clock_config.o [5]
CLOCK_ControlGate       0x8000'68c1      0x50  Code  Lc  fsl_csi.o [6]
CLOCK_ControlGate       0x8000'8161      0x4a  Code  Lc  fsl_gpio.o [6]
CLOCK_ControlGate       0x8000'8379      0x4a  Code  Lc  fsl_gpt.o [6]
CLOCK_ControlGate       0x8000'8659      0x50  Code  Lc  fsl_lpspi.o [6]
CLOCK_ControlGate       0x8000'9291      0x50  Code  Lc  fsl_lpuart.o [6]
CLOCK_ControlGate       0x8000'97ed      0x4a  Code  Lc  fsl_pit.o [6]
CLOCK_ControlGate       0x8000'9951      0x50  Code  Lc  fsl_pwm.o [6]
CLOCK_ControlGate       0x8000'a479      0x4a  Code  Lc  fsl_qtmr.o [6]
CLOCK_DeinitAudioPll    0x8000'642b       0xc  Code  Gb  fsl_clock.o [6]
CLOCK_DeinitEnetPll     0x8000'6443       0xc  Code  Gb  fsl_clock.o [6]
CLOCK_DeinitUsb2Pll     0x8000'6421       0xa  Code  Gb  fsl_clock.o [6]
CLOCK_DeinitVideoPll    0x8000'6437       0xc  Code  Gb  fsl_clock.o [6]
CLOCK_DisableClock      0x8000'5a91      0x10  Code  Lc  clock_config.o [5]
CLOCK_DisableClock      0x8000'83d3      0x10  Code  Lc  fsl_gpt.o [6]
CLOCK_DisableClock      0x8000'92f1      0x10  Code  Lc  fsl_lpuart.o [6]
CLOCK_DisableClock      0x8000'9847      0x10  Code  Lc  fsl_pit.o [6]
CLOCK_DisableClock      0x8000'99b1      0x10  Code  Lc  fsl_pwm.o [6]
CLOCK_DisableClock      0x8000'a4d3      0x10  Code  Lc  fsl_qtmr.o [6]
CLOCK_EnableClock       0x8000'6911      0x10  Code  Lc  fsl_csi.o [6]
CLOCK_EnableClock       0x8000'81ab      0x10  Code  Lc  fsl_gpio.o [6]
CLOCK_EnableClock       0x8000'83c3      0x10  Code  Lc  fsl_gpt.o [6]
CLOCK_EnableClock       0x8000'86a9      0x10  Code  Lc  fsl_lpspi.o [6]
CLOCK_EnableClock       0x8000'92e1      0x10  Code  Lc  fsl_lpuart.o [6]
CLOCK_EnableClock       0x8000'9837      0x10  Code  Lc  fsl_pit.o [6]
CLOCK_EnableClock       0x8000'99a1      0x10  Code  Lc  fsl_pwm.o [6]
CLOCK_EnableClock       0x8000'a4c3      0x10  Code  Lc  fsl_qtmr.o [6]
CLOCK_GetAhbFreq        0x8000'6217      0x18  Code  Gb  fsl_clock.o [6]
CLOCK_GetDiv            0x8000'524d      0x2a  Code  Lc  board.o [5]
CLOCK_GetFreq           0x8000'629f     0x142  Code  Gb  fsl_clock.o [6]
CLOCK_GetIpgFreq        0x8000'6263      0x18  Code  Gb  fsl_clock.o [6]
CLOCK_GetMux            0x8000'5223      0x2a  Code  Lc  board.o [5]
CLOCK_GetOscFreq        0x8000'5277      0x12  Code  Lc  board.o [5]
CLOCK_GetOscFreq        0x8000'6069      0x18  Code  Lc  fsl_clock.o [6]
CLOCK_GetPerClkFreq     0x8000'627b      0x24  Code  Gb  fsl_clock.o [6]
CLOCK_GetPeriphClkFreq  0x8000'60cf      0xaa  Code  Lc  fsl_clock.o [6]
CLOCK_GetPllBypassRefClk
                        0x8000'60ad      0x22  Code  Lc  fsl_clock.o [6]
CLOCK_GetPllFreq        0x8000'6469     0x2b0  Code  Gb  fsl_clock.o [6]
CLOCK_GetPllFreq::enetRefClkFreq
                        0x8000'0b6c      0x10  Data  Lc  fsl_clock.o [6]
CLOCK_GetPllUsb1SWFreq  0x8000'6179      0x2a  Code  Lc  fsl_clock.o [6]
CLOCK_GetRtcFreq        0x8000'6081       0x6  Code  Lc  fsl_clock.o [6]
CLOCK_GetSemcFreq       0x8000'622f      0x34  Code  Gb  fsl_clock.o [6]
CLOCK_GetSysPfdFreq     0x8000'6735      0x6a  Code  Gb  fsl_clock.o [6]
CLOCK_GetUsb1PfdFreq    0x8000'67a9      0x64  Code  Gb  fsl_clock.o [6]
CLOCK_InitArmPll        0x8000'63e1      0x40  Code  Gb  fsl_clock.o [6]
CLOCK_InitExternalClk   0x8000'61a3      0x4a  Code  Gb  fsl_clock.o [6]
CLOCK_InitRcOsc24M      0x8000'6209       0xe  Code  Gb  fsl_clock.o [6]
CLOCK_IsPllBypassed     0x8000'6087      0x10  Code  Lc  fsl_clock.o [6]
CLOCK_IsPllEnabled      0x8000'6097      0x16  Code  Lc  fsl_clock.o [6]
CLOCK_SetDiv            0x8000'59d7      0x6a  Code  Lc  clock_config.o [5]
CLOCK_SetDiv            0x8001'114b      0x64  Code  Lc  zf_driver_csi.o [11]
CLOCK_SetDiv            0x8001'3023      0x6a  Code  Lc  zf_driver_spi.o [11]
CLOCK_SetMux            0x8000'596d      0x6a  Code  Lc  clock_config.o [5]
CLOCK_SetMux            0x8001'10e1      0x6a  Code  Lc  zf_driver_csi.o [11]
CLOCK_SetMux            0x8001'2fb9      0x6a  Code  Lc  zf_driver_spi.o [11]
CLOCK_SetPllBypass      0x8000'5ab1      0x2c  Code  Lc  clock_config.o [5]
CLOCK_SetRtcXtalFreq    0x8000'5aa9       0x8  Code  Lc  clock_config.o [5]
CLOCK_SetXtalFreq       0x8000'5aa1       0x8  Code  Lc  clock_config.o [5]
CLOCK_SwitchOsc         0x8000'61ed      0x1c  Code  Gb  fsl_clock.o [6]
CORE_IRQHandler         0x8001'5b31       0x2  Code  Wk  zf_common_vector.o [8]
CSI_ClearFifo           0x8000'6be1      0x30  Code  Gb  fsl_csi.o [6]
CSI_DriverIRQHandler    0x8000'6e9f      0x14  Code  Gb  fsl_csi.o [6]
CSI_EnableFifoDmaRequest
                        0x8000'6c31      0x2e  Code  Gb  fsl_csi.o [6]
CSI_EnableInterrupts    0x8000'6c5f      0x24  Code  Gb  fsl_csi.o [6]
CSI_GetDefaultConfig    0x8000'6b7d      0x54  Code  Gb  fsl_csi.o [6]
CSI_GetInstance         0x8000'6975      0x34  Code  Lc  fsl_csi.o [6]
CSI_GetRxBufferAddr     0x8000'6a09      0x12  Code  Lc  fsl_csi.o [6]
CSI_IRQHandler          0x8000'a84f       0xc  Code  Gb  isr.o [7]
CSI_Init                0x8000'6a1b     0x114  Code  Gb  fsl_csi.o [6]
CSI_ReflashFifoDma      0x8000'6c11      0x20  Code  Gb  fsl_csi.o [6]
CSI_Reset               0x8000'6b2f      0x4e  Code  Gb  fsl_csi.o [6]
CSI_SetRxBufferAddr     0x8000'6bd1      0x10  Code  Gb  fsl_csi.o [6]
CSI_Start               0x8000'6945      0x18  Code  Lc  fsl_csi.o [6]
CSI_Stop                0x8000'695d      0x18  Code  Lc  fsl_csi.o [6]
CSI_TransferCreateHandle
                        0x8000'6c83      0x5e  Code  Gb  fsl_csi.o [6]
CSI_TransferGetEmptyBuffer
                        0x8000'69dd      0x16  Code  Lc  fsl_csi.o [6]
CSI_TransferGetEmptyBufferCount
                        0x8000'69d7       0x6  Code  Lc  fsl_csi.o [6]
CSI_TransferGetFullBuffer
                        0x8000'6d6f      0x4c  Code  Gb  fsl_csi.o [6]
CSI_TransferGetQueueDelta
                        0x8000'69a9      0x1a  Code  Lc  fsl_csi.o [6]
CSI_TransferHandleIRQ   0x8000'6dbd      0xe2  Code  Gb  fsl_csi.o [6]
CSI_TransferIncreaseQueueIdx
                        0x8000'69c3      0x14  Code  Lc  fsl_csi.o [6]
CSI_TransferPutEmptyBuffer
                        0x8000'69f3      0x16  Code  Lc  fsl_csi.o [6]
CSI_TransferStart       0x8000'6ce1      0x70  Code  Gb  fsl_csi.o [6]
CSI_TransferSubmitEmptyBuffer
                        0x8000'6d51      0x1e  Code  Gb  fsl_csi.o [6]
CSTACK$$Base            0x2006'f000             --   Gb  - Linker created -
CSTACK$$Limit           0x2007'0000             --   Gb  - Linker created -
CSU_IRQHandler          0x8001'5b9f       0x2  Code  Wk  zf_common_vector.o [8]
CTI0_ERROR_IRQHandler   0x8001'5b2d       0x2  Code  Wk  zf_common_vector.o [8]
CTI1_ERROR_IRQHandler   0x8001'5b2f       0x2  Code  Wk  zf_common_vector.o [8]
DCDC_IRQHandler         0x8001'5be5       0x2  Code  Wk  zf_common_vector.o [8]
DCP_IRQHandler          0x8001'5ba1       0x2  Code  Wk  zf_common_vector.o [8]
DCP_VMI_IRQHandler      0x8001'5ba3       0x2  Code  Wk  zf_common_vector.o [8]
DMA0_DMA16_DriverIRQHandler
                        0x8000'7bb3      0x34  Code  Gb  fsl_edma.o [6]
DMA0_DMA16_IRQHandler   0x8001'5aa5       0x8  Code  Wk  zf_common_vector.o [8]
DMA10_DMA26_DriverIRQHandler
                        0x8000'7da9      0x32  Code  Gb  fsl_edma.o [6]
DMA10_DMA26_IRQHandler  0x8001'5af5       0x8  Code  Wk  zf_common_vector.o [8]
DMA11_DMA27_DriverIRQHandler
                        0x8000'7ddb      0x32  Code  Gb  fsl_edma.o [6]
DMA11_DMA27_IRQHandler  0x8001'5afd       0x8  Code  Wk  zf_common_vector.o [8]
DMA12_DMA28_DriverIRQHandler
                        0x8000'7e0d      0x32  Code  Gb  fsl_edma.o [6]
DMA12_DMA28_IRQHandler  0x8001'5b05       0x8  Code  Wk  zf_common_vector.o [8]
DMA13_DMA29_DriverIRQHandler
                        0x8000'7e3f      0x32  Code  Gb  fsl_edma.o [6]
DMA13_DMA29_IRQHandler  0x8001'5b0d       0x8  Code  Wk  zf_common_vector.o [8]
DMA14_DMA30_DriverIRQHandler
                        0x8000'7e71      0x32  Code  Gb  fsl_edma.o [6]
DMA14_DMA30_IRQHandler  0x8001'5b15       0x8  Code  Wk  zf_common_vector.o [8]
DMA15_DMA31_DriverIRQHandler
                        0x8000'7ea9      0x32  Code  Gb  fsl_edma.o [6]
DMA15_DMA31_IRQHandler  0x8001'5b1d       0x8  Code  Wk  zf_common_vector.o [8]
DMA1_DMA17_DriverIRQHandler
                        0x8000'7be7      0x32  Code  Gb  fsl_edma.o [6]
DMA1_DMA17_IRQHandler   0x8001'5aad       0x8  Code  Wk  zf_common_vector.o [8]
DMA2_DMA18_DriverIRQHandler
                        0x8000'7c19      0x32  Code  Gb  fsl_edma.o [6]
DMA2_DMA18_IRQHandler   0x8001'5ab5       0x8  Code  Wk  zf_common_vector.o [8]
DMA3_DMA19_DriverIRQHandler
                        0x8000'7c4b      0x32  Code  Gb  fsl_edma.o [6]
DMA3_DMA19_IRQHandler   0x8001'5abd       0x8  Code  Wk  zf_common_vector.o [8]
DMA4_DMA20_DriverIRQHandler
                        0x8000'7c7d      0x32  Code  Gb  fsl_edma.o [6]
DMA4_DMA20_IRQHandler   0x8001'5ac5       0x8  Code  Wk  zf_common_vector.o [8]
DMA5_DMA21_DriverIRQHandler
                        0x8000'7caf      0x32  Code  Gb  fsl_edma.o [6]
DMA5_DMA21_IRQHandler   0x8001'5acd       0x8  Code  Wk  zf_common_vector.o [8]
DMA6_DMA22_DriverIRQHandler
                        0x8000'7ce1      0x32  Code  Gb  fsl_edma.o [6]
DMA6_DMA22_IRQHandler   0x8001'5ad5       0x8  Code  Wk  zf_common_vector.o [8]
DMA7_DMA23_DriverIRQHandler
                        0x8000'7d13      0x32  Code  Gb  fsl_edma.o [6]
DMA7_DMA23_IRQHandler   0x8001'5add       0x8  Code  Wk  zf_common_vector.o [8]
DMA8_DMA24_DriverIRQHandler
                        0x8000'7d45      0x32  Code  Gb  fsl_edma.o [6]
DMA8_DMA24_IRQHandler   0x8001'5ae5       0x8  Code  Wk  zf_common_vector.o [8]
DMA9_DMA25_DriverIRQHandler
                        0x8000'7d77      0x32  Code  Gb  fsl_edma.o [6]
DMA9_DMA25_IRQHandler   0x8001'5aed       0x8  Code  Wk  zf_common_vector.o [8]
DMA_ERROR_DriverIRQHandler
                        0x8001'5ccf       0x2  Code  Wk  zf_common_vector.o [8]
DMA_ERROR_IRQHandler    0x8001'5b25       0x8  Code  Wk  zf_common_vector.o [8]
DTCM_VAR$$Base          0x2002'1040             --   Gb  - Linker created -
DTCM_VAR$$Limit         0x2002'a640             --   Gb  - Linker created -
DbgConsole_ConvertFloatRadixNumToString
                        0x8000'7109     0x1a8  Code  Lc  fsl_debug_console.o [5]
DbgConsole_ConvertRadixNumToString
                        0x8000'6f7d     0x18a  Code  Lc  fsl_debug_console.o [5]
DbgConsole_Printf       0x8000'6ef1      0x18  Code  Gb  fsl_debug_console.o [5]
DbgConsole_PrintfFormattedData
                        0x8000'72bd     0x760  Code  Lc  fsl_debug_console.o [5]
DbgConsole_PrintfPaddingCharacter
                        0x8000'6f51      0x2c  Code  Lc  fsl_debug_console.o [5]
DbgConsole_Putchar      0x8000'6f31      0x20  Code  Gb  fsl_debug_console.o [5]
DbgConsole_Vprintf      0x8000'6f09      0x28  Code  Gb  fsl_debug_console.o [5]
DebugMon_Handler        0x8001'5a9f       0x2  Code  Gb  zf_common_vector.o [8]
DefaultISR              0x7000'27e5            Code  Wk  startup_MIMXRT1064.o [5]
DisableGlobalIRQ        0x8000'fa69       0x8  Code  Lc  zf_common_interrupt.o [8]
EDMA_GetChannelStatusFlags
                        0x8000'7a1d      0x46  Code  Gb  fsl_edma.o [6]
EDMA_HandleIRQ          0x8000'7a6d     0x146  Code  Gb  fsl_edma.o [6]
ENC1_IRQHandler         0x8001'5c7d       0x2  Code  Wk  zf_common_vector.o [8]
ENC2_IRQHandler         0x8001'5c7f       0x2  Code  Wk  zf_common_vector.o [8]
ENC3_IRQHandler         0x8001'5c81       0x2  Code  Wk  zf_common_vector.o [8]
ENC4_IRQHandler         0x8001'5c83       0x2  Code  Wk  zf_common_vector.o [8]
ENET2_1588_Timer_DriverIRQHandler
                        0x8000'8009       0xe  Code  Gb  fsl_enet.o [6]
ENET2_1588_Timer_IRQHandler
                        0x8001'5cb3       0x8  Code  Wk  zf_common_vector.o [8]
ENET2_DriverIRQHandler  0x8000'7ffb       0xe  Code  Gb  fsl_enet.o [6]
ENET2_IRQHandler        0x8001'5cab       0x8  Code  Wk  zf_common_vector.o [8]
ENET_1588_Timer_DriverIRQHandler
                        0x8000'7fed       0xe  Code  Gb  fsl_enet.o [6]
ENET_1588_Timer_IRQHandler
                        0x8001'5c5d       0x8  Code  Wk  zf_common_vector.o [8]
ENET_CommonFrame0IRQHandler
                        0x8000'7f2d      0x8c  Code  Gb  fsl_enet.o [6]
ENET_DriverIRQHandler   0x8000'7fdf       0xe  Code  Gb  fsl_enet.o [6]
ENET_GetInstance        0x8000'7eed      0x34  Code  Gb  fsl_enet.o [6]
ENET_IRQHandler         0x8001'5c55       0x8  Code  Wk  zf_common_vector.o [8]
ENET_Ptp1588IRQHandler  0x8000'7fb9      0x26  Code  Gb  fsl_enet.o [6]
EWM_IRQHandler          0x8001'5c19       0x2  Code  Wk  zf_common_vector.o [8]
EnableIRQ               0x8000'6921      0x24  Code  Lc  fsl_csi.o [6]
EnableIRQ               0x8000'fa45      0x24  Code  Lc  zf_common_interrupt.o [8]
EnableIRQ               0x8001'1ee3      0x24  Code  Lc  zf_driver_pit.o [11]
FLEXIO1_DriverIRQHandler
                        0x8000'8131       0x8  Code  Gb  fsl_flexio.o [6]
FLEXIO1_IRQHandler      0x8001'5c05       0x8  Code  Wk  zf_common_vector.o [8]
FLEXIO2_DriverIRQHandler
                        0x8000'8139       0x8  Code  Gb  fsl_flexio.o [6]
FLEXIO2_IRQHandler      0x8001'5c0d       0x8  Code  Wk  zf_common_vector.o [8]
FLEXIO3_DriverIRQHandler
                        0x8000'8141       0x8  Code  Gb  fsl_flexio.o [6]
FLEXIO3_IRQHandler      0x8001'5cc5       0x8  Code  Wk  zf_common_vector.o [8]
FLEXIO_CommonIRQHandler
                        0x8000'80e1      0x42  Code  Lc  fsl_flexio.o [6]
FLEXRAM_IRQHandler      0x8001'5b8b       0x2  Code  Wk  zf_common_vector.o [8]
FLEXSPI2_DriverIRQHandler
                        0x8001'5cd1       0x2  Code  Wk  zf_common_vector.o [8]
FLEXSPI2_IRQHandler     0x8001'5c33       0x8  Code  Wk  zf_common_vector.o [8]
FLEXSPI_DriverIRQHandler
                        0x8001'5cd3       0x2  Code  Wk  zf_common_vector.o [8]
FLEXSPI_IRQHandler      0x8001'5c3b       0x8  Code  Wk  zf_common_vector.o [8]
GPC_IRQHandler          0x8001'5c1f       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_Combined_0_15_IRQHandler
                        0x8000'aba5      0x18  Code  Gb  isr.o [7]
GPIO1_Combined_16_31_IRQHandler
                        0x8000'abbd      0x24  Code  Gb  isr.o [7]
GPIO1_INT0_IRQHandler   0x8001'5beb       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT1_IRQHandler   0x8001'5bed       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT2_IRQHandler   0x8001'5bef       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT3_IRQHandler   0x8001'5bf1       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT4_IRQHandler   0x8001'5bf3       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT5_IRQHandler   0x8001'5bf5       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT6_IRQHandler   0x8001'5bf7       0x2  Code  Wk  zf_common_vector.o [8]
GPIO1_INT7_IRQHandler   0x8001'5bf9       0x2  Code  Wk  zf_common_vector.o [8]
GPIO2_Combined_0_15_IRQHandler
                        0x8000'abe1      0x1e  Code  Gb  isr.o [7]
GPIO2_Combined_16_31_IRQHandler
                        0x8000'abff      0x24  Code  Gb  isr.o [7]
GPIO3_Combined_0_15_IRQHandler
                        0x8000'ac23      0x1c  Code  Gb  isr.o [7]
GPIO3_Combined_16_31_IRQHandler
                        0x8001'5bfb       0x2  Code  Wk  zf_common_vector.o [8]
GPIO4_Combined_0_15_IRQHandler
                        0x8001'5bfd       0x2  Code  Wk  zf_common_vector.o [8]
GPIO4_Combined_16_31_IRQHandler
                        0x8001'5bff       0x2  Code  Wk  zf_common_vector.o [8]
GPIO5_Combined_0_15_IRQHandler
                        0x8001'5c01       0x2  Code  Wk  zf_common_vector.o [8]
GPIO5_Combined_16_31_IRQHandler
                        0x8001'5c03       0x2  Code  Wk  zf_common_vector.o [8]
GPIO6_7_8_9_IRQHandler  0x8001'5ccd       0x2  Code  Wk  zf_common_vector.o [8]
GPIO_ClearPinsInterruptFlags
                        0x8000'a821      0x10  Code  Lc  isr.o [7]
GPIO_ClearPinsOutput    0x8000'fb01      0x10  Code  Lc  zf_device_imu660ra.o [10]
GPIO_ClearPinsOutput    0x8001'0175      0x10  Code  Lc  zf_device_tft180.o [10]
GPIO_ClearPinsOutput    0x8001'1c29      0x10  Code  Lc  zf_driver_gpio.o [11]
GPIO_ClearPinsOutput    0x8001'297d      0x10  Code  Lc  zf_driver_soft_iic.o [11]
GPIO_GetInstance        0x8000'81d1      0x2e  Code  Lc  fsl_gpio.o [6]
GPIO_GetPinsInterruptFlags
                        0x8000'a811       0xc  Code  Lc  isr.o [7]
GPIO_PinInit            0x8000'81ff      0x78  Code  Gb  fsl_gpio.o [6]
GPIO_PinReadPadStatus   0x8001'1c39      0x26  Code  Lc  zf_driver_gpio.o [11]
GPIO_PinSetInterruptConfig
                        0x8000'82cd      0x92  Code  Gb  fsl_gpio.o [6]
GPIO_PinWrite           0x8000'8277      0x36  Code  Gb  fsl_gpio.o [6]
GPIO_PortClear          0x8000'fafb       0x6  Code  Lc  zf_device_imu660ra.o [10]
GPIO_PortClear          0x8001'016f       0x6  Code  Lc  zf_device_tft180.o [10]
GPIO_PortClear          0x8001'1c23       0x6  Code  Lc  zf_driver_gpio.o [11]
GPIO_PortClear          0x8001'2977       0x6  Code  Lc  zf_driver_soft_iic.o [11]
GPIO_PortClearInterruptFlags
                        0x8000'a81d       0x4  Code  Lc  isr.o [7]
GPIO_PortGetInterruptFlags
                        0x8000'a80d       0x4  Code  Lc  isr.o [7]
GPIO_PortSet            0x8000'fae5       0x6  Code  Lc  zf_device_imu660ra.o [10]
GPIO_PortSet            0x8001'0159       0x6  Code  Lc  zf_device_tft180.o [10]
GPIO_PortSet            0x8001'1c0d       0x6  Code  Lc  zf_driver_gpio.o [11]
GPIO_PortSet            0x8001'2961       0x6  Code  Lc  zf_driver_soft_iic.o [11]
GPIO_ReadPadStatus      0x8001'1c5f      0x10  Code  Lc  zf_driver_gpio.o [11]
GPIO_SetPinInterruptConfig
                        0x8000'81bb      0x16  Code  Lc  fsl_gpio.o [6]
GPIO_SetPinsOutput      0x8000'faeb      0x10  Code  Lc  zf_device_imu660ra.o [10]
GPIO_SetPinsOutput      0x8001'015f      0x10  Code  Lc  zf_device_tft180.o [10]
GPIO_SetPinsOutput      0x8001'1c13      0x10  Code  Lc  zf_driver_gpio.o [11]
GPIO_SetPinsOutput      0x8001'2967      0x10  Code  Lc  zf_driver_soft_iic.o [11]
GPR_IRQ_IRQHandler      0x8001'5b91       0x2  Code  Wk  zf_common_vector.o [8]
GPT1_IRQHandler         0x8001'5c25       0x2  Code  Wk  zf_common_vector.o [8]
GPT2_IRQHandler         0x8001'5c27       0x2  Code  Wk  zf_common_vector.o [8]
GPT_ClearStatusFlags    0x8001'15a3       0x8  Code  Lc  zf_driver_delay.o [11]
GPT_Deinit              0x8000'851d      0x1a  Code  Gb  fsl_gpt.o [6]
GPT_GetDefaultConfig    0x8000'8537      0x48  Code  Gb  fsl_gpt.o [6]
GPT_GetInstance         0x8000'8453      0x2e  Code  Lc  fsl_gpt.o [6]
GPT_GetStatusFlags      0x8001'1599       0xa  Code  Lc  zf_driver_delay.o [11]
GPT_Init                0x8000'8481      0x9c  Code  Gb  fsl_gpt.o [6]
GPT_SetClockDivider     0x8000'842b      0x28  Code  Lc  fsl_gpt.o [6]
GPT_SetClockSource      0x8000'83f3      0x38  Code  Lc  fsl_gpt.o [6]
GPT_SetOutputCompareValue
                        0x8001'156d      0x2c  Code  Lc  zf_driver_delay.o [11]
GPT_SoftwareReset       0x8000'83e3      0x10  Code  Lc  fsl_gpt.o [6]
GPT_StartTimer          0x8001'1559       0xa  Code  Lc  zf_driver_delay.o [11]
GPT_StopTimer           0x8001'1563       0xa  Code  Lc  zf_driver_delay.o [11]
GetWheelSpeed           0x8000'5499      0xbc  Code  Gb  ce.o [1]
HEAP$$Base              0x2002'a640             --   Gb  - Linker created -
HEAP$$Limit             0x2002'aa40             --   Gb  - Linker created -
HardFault_Handler       0x8001'5a95       0x2  Code  Gb  zf_common_vector.o [8]
IOMUXC_SetPinConfig     0x8001'1bfd      0x10  Code  Lc  zf_driver_gpio.o [11]
IOMUXC_SetPinMux        0x8001'1be1      0x1c  Code  Lc  zf_driver_gpio.o [11]
I_ex                    0x2000'0eec       0x4  Data  Gb  siyuanshu.o [1]
I_ey                    0x2000'0ef0       0x4  Data  Gb  siyuanshu.o [1]
I_ez                    0x2000'0ef4       0x4  Data  Gb  siyuanshu.o [1]
ImagePerspective_Init   0x8000'c065     0x12e  Code  Gb  sxt.o [1]
ImagePerspective_Init::BlackColor
                        0x2002'1013       0x1  Data  Lc  sxt.o [1]
InStream_Read           0x7000'2455      0x20  Code  Lc  lz77_init.o [14]
InStream_StepRegion     0x7000'2441      0x14  Code  Lc  lz77_init.o [14]
Init                    0x8001'592b     0x168  Code  Gb  init.o [1]
InverseKinematics       0x8000'5865      0x8e  Code  Gb  ce.o [1]
Island_State            0x2002'0ebc       0x4  Data  Gb  yuansu.o [1]
KPP_IRQHandler          0x8001'5b8d       0x2  Code  Wk  zf_common_vector.o [8]
Kd1                     0x2000'0c30       0x4  Data  Gb  menu.o [1]
Ki1                     0x2000'0ea8       0x4  Data  Gb  menu.o [1]
LCDIF_IRQHandler        0x8001'5b93       0x2  Code  Wk  zf_common_vector.o [8]
LPI2C1_DriverIRQHandler
                        0x8000'85ef       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C1_IRQHandler       0x8001'5b3b       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C2_DriverIRQHandler
                        0x8000'85fb       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C2_IRQHandler       0x8001'5b43       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C3_DriverIRQHandler
                        0x8000'860d       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C3_IRQHandler       0x8001'5b4b       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C4_DriverIRQHandler
                        0x8000'861d       0xc  Code  Gb  fsl_lpi2c.o [6]
LPI2C4_IRQHandler       0x8001'5b53       0x8  Code  Wk  zf_common_vector.o [8]
LPI2C_CommonIRQHandler  0x8000'85ad      0x42  Code  Lc  fsl_lpi2c.o [6]
LPSPI1_DriverIRQHandler
                        0x8000'91ad      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI1_IRQHandler       0x8001'5b5b       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI2_DriverIRQHandler
                        0x8000'91d1      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI2_IRQHandler       0x8001'5b63       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI3_DriverIRQHandler
                        0x8000'91f5      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI3_IRQHandler       0x8001'5b6b       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI4_DriverIRQHandler
                        0x8000'9219      0x24  Code  Gb  fsl_lpspi.o [6]
LPSPI4_IRQHandler       0x8001'5b73       0x8  Code  Wk  zf_common_vector.o [8]
LPSPI_CheckTransferArgument
                        0x8000'8b8b      0xc6  Code  Gb  fsl_lpspi.o [6]
LPSPI_ClearStatusFlags  0x8000'86f7       0x4  Code  Lc  fsl_lpspi.o [6]
LPSPI_ClearStatusFlags  0x8001'30a9       0x4  Code  Lc  zf_driver_spi.o [11]
LPSPI_CombineWriteData  0x8000'8f8d      0xf8  Code  Lc  fsl_lpspi.o [6]
LPSPI_CommonIRQHandler  0x8000'9181      0x2c  Code  Lc  fsl_lpspi.o [6]
LPSPI_DisableInterrupts
                        0x8001'30ad       0x8  Code  Lc  zf_driver_spi.o [11]
LPSPI_Enable            0x8000'86b9      0x1c  Code  Lc  fsl_lpspi.o [6]
LPSPI_Enable            0x8001'308d      0x1c  Code  Lc  zf_driver_spi.o [11]
LPSPI_FlushFifo         0x8000'8711      0x1a  Code  Lc  fsl_lpspi.o [6]
LPSPI_FlushFifo         0x8001'30b5      0x1a  Code  Lc  zf_driver_spi.o [11]
LPSPI_GetInstance       0x8000'8747      0x44  Code  Gb  fsl_lpspi.o [6]
LPSPI_GetRxFifoCount    0x8000'86ef       0x8  Code  Lc  fsl_lpspi.o [6]
LPSPI_GetRxFifoSize     0x8000'86d9       0xe  Code  Lc  fsl_lpspi.o [6]
LPSPI_GetStatusFlags    0x8000'86d5       0x4  Code  Lc  fsl_lpspi.o [6]
LPSPI_GetTxFifoCount    0x8000'86e7       0x8  Code  Lc  fsl_lpspi.o [6]
LPSPI_IsMaster          0x8000'8709       0x8  Code  Lc  fsl_lpspi.o [6]
LPSPI_MasterGetDefaultConfig
                        0x8000'8899      0x7e  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterInit        0x8000'879f      0xfa  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterSetBaudRate
                        0x8000'894f      0xa8  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterSetDelayScaler
                        0x8000'89f7      0x4c  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterSetDelayTimes
                        0x8000'8a43     0x148  Code  Gb  fsl_lpspi.o [6]
LPSPI_MasterTransferBlocking
                        0x8000'8c55     0x2f8  Code  Gb  fsl_lpspi.o [6]
LPSPI_ReadData          0x8000'8743       0x4  Code  Lc  fsl_lpspi.o [6]
LPSPI_Reset             0x8000'8917      0x16  Code  Gb  fsl_lpspi.o [6]
LPSPI_SeparateReadData  0x8000'9085      0xe8  Code  Lc  fsl_lpspi.o [6]
LPSPI_SetDummyData      0x8000'878b      0x14  Code  Gb  fsl_lpspi.o [6]
LPSPI_SetFifoWatermarks
                        0x8000'872b      0x14  Code  Lc  fsl_lpspi.o [6]
LPSPI_SetMasterSlaveMode
                        0x8000'86fb       0xe  Code  Lc  fsl_lpspi.o [6]
LPSPI_SetOnePcsPolarity
                        0x8000'892d      0x22  Code  Lc  fsl_lpspi.o [6]
LPSPI_TxFifoReady       0x8000'916d      0x14  Code  Lc  fsl_lpspi.o [6]
LPSPI_WriteData         0x8000'873f       0x4  Code  Lc  fsl_lpspi.o [6]
LPUART1_IRQHandler      0x8000'aacd      0x22  Code  Gb  isr.o [7]
LPUART2_IRQHandler      0x8000'aaef      0x1e  Code  Gb  isr.o [7]
LPUART3_IRQHandler      0x8000'ab0d      0x16  Code  Gb  isr.o [7]
LPUART4_IRQHandler      0x8000'ab23      0x1e  Code  Gb  isr.o [7]
LPUART5_IRQHandler      0x8000'ab49      0x20  Code  Gb  isr.o [7]
LPUART6_IRQHandler      0x8000'ab69      0x16  Code  Gb  isr.o [7]
LPUART7_DriverIRQHandler
                        0x8000'97a9      0x14  Code  Gb  fsl_lpuart.o [6]
LPUART7_IRQHandler      0x8001'5b33       0x8  Code  Wk  zf_common_vector.o [8]
LPUART8_IRQHandler      0x8000'ab85      0x20  Code  Gb  isr.o [7]
LPUART_ClearStatusFlags
                        0x8000'96eb      0x42  Code  Gb  fsl_lpuart.o [6]
LPUART_Deinit           0x8000'95ab      0x3e  Code  Gb  fsl_lpuart.o [6]
LPUART_DisableInterrupts
                        0x8000'9695      0x40  Code  Gb  fsl_lpuart.o [6]
LPUART_EnableInterrupts
                        0x8000'9655      0x40  Code  Gb  fsl_lpuart.o [6]
LPUART_GetDefaultConfig
                        0x8000'95e9      0x6c  Code  Gb  fsl_lpuart.o [6]
LPUART_GetInstance      0x8000'9313      0x34  Code  Gb  fsl_lpuart.o [6]
LPUART_GetStatusFlags   0x8000'96d5      0x16  Code  Gb  fsl_lpuart.o [6]
LPUART_Init             0x8000'9347     0x264  Code  Gb  fsl_lpuart.o [6]
LPUART_ReadByte         0x8001'3abd      0x2e  Code  Lc  zf_driver_uart.o [11]
LPUART_SoftwareReset    0x8000'9301      0x12  Code  Lc  fsl_lpuart.o [6]
LPUART_WriteBlocking    0x8000'972d      0x42  Code  Gb  fsl_lpuart.o [6]
LPUART_WriteByte        0x8001'3ab5       0x8  Code  Lc  zf_driver_uart.o [11]
L_corner_col            0x2002'0ff8       0x2  Data  Gb  sxt.o [1]
L_corner_flag           0x2002'0ff4       0x2  Data  Gb  sxt.o [1]
L_corner_row            0x2002'0ff6       0x2  Data  Gb  sxt.o [1]
L_start_x               0x2002'0ff0       0x2  Data  Gb  sxt.o [1]
Left_Line               0x2001'd134     0x1e0  Data  Gb  sxt.o [1]
MemManage_Handler       0x8001'5a97       0x2  Code  Gb  zf_common_vector.o [8]
Mid_Line                0x2001'd4f4     0x1e0  Data  Gb  sxt.o [1]
NMI_Handler             0x8001'5a93       0x2  Code  Gb  zf_common_vector.o [8]
OutStream_Write         0x7000'2475      0x46  Code  Lc  lz77_init.o [14]
PID_Compute             0x8000'562d     0x100  Code  Gb  ce.o [1]
PIT_ClearStatusFlags    0x8000'a841       0xe  Code  Lc  isr.o [7]
PIT_Deinit              0x8000'98f9      0x1e  Code  Gb  fsl_pit.o [6]
PIT_EnableInterrupts    0x8001'1f6b      0x20  Code  Lc  zf_driver_pit.o [11]
PIT_GetDefaultConfig    0x8001'1f07      0x1c  Code  Lc  zf_driver_pit.o [11]
PIT_GetInstance         0x8000'9857      0x2e  Code  Lc  fsl_pit.o [6]
PIT_GetStatusFlags      0x8000'a831      0x10  Code  Lc  isr.o [7]
PIT_IRQHandler          0x8000'a85d     0x270  Code  Gb  isr.o [7]
PIT_Init                0x8000'9885      0x74  Code  Gb  fsl_pit.o [6]
PIT_SetTimerChainMode   0x8001'1f23      0x48  Code  Lc  zf_driver_pit.o [11]
PIT_SetTimerPeriod      0x8001'1f8b      0x2a  Code  Lc  zf_driver_pit.o [11]
PIT_StartTimer          0x8001'1fb5      0x1e  Code  Lc  zf_driver_pit.o [11]
PMU_EVENT_IRQHandler    0x8001'5bd5       0x2  Code  Wk  zf_common_vector.o [8]
PORTPTR                 0x2000'0a68      0x2c  Data  Gb  zf_driver_gpio.o [11]
PWM1_0_IRQHandler       0x8001'5c29       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_1_IRQHandler       0x8001'5c2b       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_2_IRQHandler       0x8001'5c2d       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_3_IRQHandler       0x8001'5c2f       0x2  Code  Wk  zf_common_vector.o [8]
PWM1_FAULT_IRQHandler   0x8001'5c31       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_0_IRQHandler       0x8001'5c8d       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_1_IRQHandler       0x8001'5c8f       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_2_IRQHandler       0x8001'5c91       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_3_IRQHandler       0x8001'5c93       0x2  Code  Wk  zf_common_vector.o [8]
PWM2_FAULT_IRQHandler   0x8001'5c95       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_0_IRQHandler       0x8001'5c97       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_1_IRQHandler       0x8001'5c99       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_2_IRQHandler       0x8001'5c9b       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_3_IRQHandler       0x8001'5c9d       0x2  Code  Wk  zf_common_vector.o [8]
PWM3_FAULT_IRQHandler   0x8001'5c9f       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_0_IRQHandler       0x8001'5ca1       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_1_IRQHandler       0x8001'5ca3       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_2_IRQHandler       0x8001'5ca5       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_3_IRQHandler       0x8001'5ca7       0x2  Code  Wk  zf_common_vector.o [8]
PWM4_FAULT_IRQHandler   0x8001'5ca9       0x2  Code  Wk  zf_common_vector.o [8]
PWMPTR                  0x2000'0bdc      0x14  Data  Lc  zf_driver_pwm.o [11]
PWM_Deinit              0x8000'9bd3      0x34  Code  Gb  fsl_pwm.o [6]
PWM_GetComplementU16    0x8000'99c1       0xa  Code  Lc  fsl_pwm.o [6]
PWM_GetDefaultConfig    0x8000'9c07      0x54  Code  Gb  fsl_pwm.o [6]
PWM_GetInstance         0x8000'99cb      0x34  Code  Lc  fsl_pwm.o [6]
PWM_Init                0x8000'99ff     0x1d4  Code  Gb  fsl_pwm.o [6]
PWM_SetPwmLdok          0x8001'206b      0x36  Code  Lc  zf_driver_pwm.o [11]
PWM_SetupPwm            0x8000'9c61     0x52a  Code  Gb  fsl_pwm.o [6]
PWM_StartTimer          0x8001'2055      0x16  Code  Lc  zf_driver_pwm.o [11]
PWM_UpdatePwmDutycycleHighAccuracy
                        0x8000'a191     0x296  Code  Gb  fsl_pwm.o [6]
PXP_IRQHandler          0x8001'5b95       0x2  Code  Wk  zf_common_vector.o [8]
PendSV_Handler          0x8001'5aa1       0x2  Code  Gb  zf_common_vector.o [8]
PerImg_ip               0x2000'0f34  0x1'2c00  Data  Gb  sxt.o [1]
QTMR_Deinit             0x8000'a5af      0x30  Code  Gb  fsl_qtmr.o [6]
QTMR_GetCurrentTimerCount
                        0x8001'1621       0xa  Code  Lc  zf_driver_encoder.o [11]
QTMR_GetDefaultConfig   0x8000'a5df      0x44  Code  Gb  fsl_qtmr.o [6]
QTMR_GetInstance        0x8000'a4e3      0x2e  Code  Lc  fsl_qtmr.o [6]
QTMR_Init               0x8000'a511      0x9e  Code  Gb  fsl_qtmr.o [6]
QTMR_StartTimer         0x8001'162b      0x26  Code  Lc  zf_driver_encoder.o [11]
Q_info_q0               0x2000'0c4c       0x4  Data  Gb  siyuanshu.o [1]
Q_info_q1               0x2000'0ef8       0x4  Data  Gb  siyuanshu.o [1]
Q_info_q2               0x2000'0efc       0x4  Data  Gb  siyuanshu.o [1]
Q_info_q3               0x2000'0f00       0x4  Data  Gb  siyuanshu.o [1]
RTWDOG_IRQHandler       0x8001'5c17       0x2  Code  Wk  zf_common_vector.o [8]
RW$$Base                0x8000'0400             --   Gb  - Linker created -
RW$$Limit               0x8001'5e40             --   Gb  - Linker created -
R_corner_col            0x2002'0ffe       0x2  Data  Gb  sxt.o [1]
R_corner_flag           0x2002'0ffa       0x2  Data  Gb  sxt.o [1]
R_corner_row            0x2002'0ffc       0x2  Data  Gb  sxt.o [1]
R_start_x               0x2002'0ff2       0x2  Data  Gb  sxt.o [1]
Region$$Table$$Base     0x7000'286c             --   Gb  - Linker created -
Region$$Table$$Limit    0x7000'28bc             --   Gb  - Linker created -
Reserved115_IRQHandler  0x8001'5c23       0x2  Code  Wk  zf_common_vector.o [8]
Reserved143_IRQHandler  0x8001'5c79       0x2  Code  Wk  zf_common_vector.o [8]
Reserved144_IRQHandler  0x8001'5c7b       0x2  Code  Wk  zf_common_vector.o [8]
Reserved171_IRQHandler  0x8001'5cc3       0x2  Code  Wk  zf_common_vector.o [8]
Reserved68_IRQHandler   0x8001'5ba5       0x2  Code  Wk  zf_common_vector.o [8]
Reserved78_IRQHandler   0x8001'5bd7       0x2  Code  Wk  zf_common_vector.o [8]
Reserved86_IRQHandler   0x8001'5be7       0x2  Code  Wk  zf_common_vector.o [8]
Reserved87_IRQHandler   0x8001'5be9       0x2  Code  Wk  zf_common_vector.o [8]
Reset_Handler           0x7000'277d            Code  Gb  startup_MIMXRT1064.o [5]
Right_Line              0x2001'd314     0x1e0  Data  Gb  sxt.o [1]
SAI1_DriverIRQHandler   0x8000'a66f      0x52  Code  Gb  fsl_sai.o [6]
SAI1_IRQHandler         0x8001'5bad       0x8  Code  Wk  zf_common_vector.o [8]
SAI2_DriverIRQHandler   0x8000'a6c1      0x52  Code  Gb  fsl_sai.o [6]
SAI2_IRQHandler         0x8001'5bb5       0x8  Code  Wk  zf_common_vector.o [8]
SAI3_RX_DriverIRQHandler
                        0x8000'a73d      0x2a  Code  Gb  fsl_sai.o [6]
SAI3_RX_IRQHandler      0x8001'5bbd       0x8  Code  Wk  zf_common_vector.o [8]
SAI3_TX_DriverIRQHandler
                        0x8000'a713      0x2a  Code  Gb  fsl_sai.o [6]
SAI3_TX_IRQHandler      0x8001'5bc5       0x8  Code  Wk  zf_common_vector.o [8]
SAI_RxGetEnabledInterruptStatus
                        0x8000'a645      0x16  Code  Lc  fsl_sai.o [6]
SAI_TxGetEnabledInterruptStatus
                        0x8000'a65b      0x14  Code  Lc  fsl_sai.o [6]
SEMC_IRQHandler         0x8001'5c43       0x2  Code  Wk  zf_common_vector.o [8]
SJC_IRQHandler          0x8001'5ba9       0x2  Code  Wk  zf_common_vector.o [8]
SNVS_HP_WRAPPER_IRQHandler
                        0x8001'5b99       0x2  Code  Wk  zf_common_vector.o [8]
SNVS_HP_WRAPPER_TZ_IRQHandler
                        0x8001'5b9b       0x2  Code  Wk  zf_common_vector.o [8]
SNVS_LP_WRAPPER_IRQHandler
                        0x8001'5b9d       0x2  Code  Wk  zf_common_vector.o [8]
SPDIF_DriverIRQHandler  0x8000'a78d      0x36  Code  Gb  fsl_spdif.o [6]
SPDIF_IRQHandler        0x8001'5bcd       0x8  Code  Wk  zf_common_vector.o [8]
SRC_IRQHandler          0x8001'5c21       0x2  Code  Wk  zf_common_vector.o [8]
SVC_Handler             0x8001'5a9d       0x2  Code  Gb  zf_common_vector.o [8]
SetWheelSpeed           0x8000'5735     0x12c  Code  Gb  ce.o [1]
SysTick_Handler         0x8001'5aa3       0x2  Code  Wk  zf_common_vector.o [8]
SystemCoreClock         0x2000'0c5c       0x4  Data  Gb  system_MIMXRT1064.o [5]
SystemInit              0x7000'2655      0xe4  Code  Gb  system_MIMXRT1064.o [5]
SystemInitHook          0x7000'2779       0x2  Code  Wk  system_MIMXRT1064.o [5]
TEMP_LOW_HIGH_IRQHandler
                        0x8001'5bd9       0x2  Code  Wk  zf_common_vector.o [8]
TEMP_PANIC_IRQHandler   0x8001'5bdb       0x2  Code  Wk  zf_common_vector.o [8]
TMR1_IRQHandler         0x8001'5c85       0x2  Code  Wk  zf_common_vector.o [8]
TMR2_IRQHandler         0x8001'5c87       0x2  Code  Wk  zf_common_vector.o [8]
TMR3_IRQHandler         0x8001'5c89       0x2  Code  Wk  zf_common_vector.o [8]
TMR4_IRQHandler         0x8001'5c8b       0x2  Code  Wk  zf_common_vector.o [8]
TRNG_IRQHandler         0x8001'5ba7       0x2  Code  Wk  zf_common_vector.o [8]
TSC_DIG_IRQHandler      0x8001'5b8f       0x2  Code  Wk  zf_common_vector.o [8]
USB_DeviceControl       0x8000'd6cd      0x2e  Code  Lc  usb_device_dci.o [4]
USB_DeviceEhciCancelControlPipe
                        0x8000'd8c1     0x112  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciFillSetupBuffer
                        0x8000'd845      0x7c  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptPortChange
                        0x8000'dd11      0x52  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptReset
                        0x8000'dd63      0x66  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptSof
                        0x8000'ddc9       0x2  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciInterruptTokenDone
                        0x8000'd9d3     0x33e  Code  Lc  usb_device_ehci.o [4]
USB_DeviceEhciIsrFunction
                        0x8000'ddcb      0x4a  Code  Gb  usb_device_ehci.o [4]
USB_DeviceNotification  0x8000'd75b      0xbe  Code  Lc  usb_device_dci.o [4]
USB_DeviceNotificationTrigger
                        0x8000'd819      0x2a  Code  Gb  usb_device_dci.o [4]
USB_DeviceResetNotification
                        0x8000'd6fb      0x60  Code  Lc  usb_device_dci.o [4]
USB_OTG1_IRQHandler     0x8001'4081       0xe  Code  Gb  zf_driver_usb_cdc.o [11]
USB_OTG2_IRQHandler     0x8001'408f       0xe  Code  Gb  zf_driver_usb_cdc.o [11]
USB_PHY1_IRQHandler     0x8001'5bdd       0x2  Code  Wk  zf_common_vector.o [8]
USB_PHY2_IRQHandler     0x8001'5bdf       0x2  Code  Wk  zf_common_vector.o [8]
USDHC1_DriverIRQHandler
                        0x8000'a7d5      0x16  Code  Gb  fsl_usdhc.o [6]
USDHC1_IRQHandler       0x8001'5c45       0x8  Code  Wk  zf_common_vector.o [8]
USDHC2_DriverIRQHandler
                        0x8000'a7eb      0x16  Code  Gb  fsl_usdhc.o [6]
USDHC2_IRQHandler       0x8001'5c4d       0x8  Code  Wk  zf_common_vector.o [8]
UpdateRobotPosition     0x8000'5599      0x94  Code  Gb  ce.o [1]
UsageFault_Handler      0x8001'5a9b       0x2  Code  Gb  zf_common_vector.o [8]
WDOG1_IRQHandler        0x8001'5c15       0x2  Code  Wk  zf_common_vector.o [8]
WDOG2_IRQHandler        0x8001'5b97       0x2  Code  Wk  zf_common_vector.o [8]
XBAR1_IRQ_0_1_IRQHandler
                        0x8001'5c65       0x2  Code  Wk  zf_common_vector.o [8]
XBAR1_IRQ_2_3_IRQHandler
                        0x8001'5c67       0x2  Code  Wk  zf_common_vector.o [8]
ZI$$Base                0x2000'0ca0             --   Gb  - Linker created -
ZI$$Limit               0x2002'102c             --   Gb  - Linker created -
_PrintfTiny             0x8001'5d1d     0x124  Code  Gb  xprintftiny.o [12]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'5955      0x18  Code  Lc  clock_config.o [5]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'68a9      0x18  Code  Lc  fsl_csi.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'8149      0x18  Code  Lc  fsl_gpio.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'8361      0x18  Code  Lc  fsl_gpt.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'8641      0x18  Code  Lc  fsl_lpspi.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'9279      0x18  Code  Lc  fsl_lpuart.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'97d5      0x18  Code  Lc  fsl_pit.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'9939      0x18  Code  Lc  fsl_pwm.o [6]
_SDK_AtomicLocalClearAndSet4Byte
                        0x8000'a461      0x18  Code  Lc  fsl_qtmr.o [6]
_SProut                 0x8001'5cf7       0xa  Code  Gb  xsprout.o [12]
__CSTACK_ADDRESS {Abs}  0x2007'0000            Data  Gb  <internal module>
__NVIC_EnableIRQ        0x8000'6889      0x20  Code  Lc  fsl_csi.o [6]
__NVIC_EnableIRQ        0x8000'f9fb      0x1e  Code  Lc  zf_common_interrupt.o [8]
__NVIC_EnableIRQ        0x8001'1ec5      0x1e  Code  Lc  zf_driver_pit.o [11]
__NVIC_SetPriority      0x8000'fa19      0x2c  Code  Lc  zf_common_interrupt.o [8]
__NVIC_SetPriorityGrouping
                        0x8000'f9dd      0x1e  Code  Lc  zf_common_interrupt.o [8]
__RAM_VECTOR_TABLE_SIZE {Abs}
                              0x400            Data  Gb  <internal module>
__VECTOR_RAM {Abs}      0x8000'0000            Data  Gb  <internal module>
__VECTOR_TABLE {Abs}    0x7000'2000            Data  Gb  <internal module>
__aeabi_assert          0x8000'604d      0x1c  Code  Gb  fsl_assert.o [5]
__aeabi_d2lz            0x8001'4af1            Code  Gb  DblToS64.o [13]
__aeabi_l2d             0x8001'4b59            Code  Gb  S64ToDbl.o [13]
__aeabi_ldiv0           0x8001'5411            Code  Gb  I64DivZer.o [14]
__aeabi_ldivmod         0x8001'487d            Code  Gb  I64DivMod.o [14]
__aeabi_memcpy          0x8001'476d            Code  Gb  ABImemcpy.o [14]
__aeabi_memcpy4         0x8001'478d            Code  Gb  ABImemcpy.o [14]
__aeabi_memcpy8         0x8001'478d            Code  Gb  ABImemcpy.o [14]
__aeabi_memset          0x8001'4815            Code  Gb  ABImemset.o [14]
__aeabi_uldivmod        0x8001'48c1            Code  Gb  I64DivMod.o [14]
__basic_free            0x8001'5381      0x16  Code  Gb  heap0.o [12]
__basic_free_intern     0x8001'5397      0x74  Code  Lc  heap0.o [12]
__basic_malloc          0x8001'52d5      0x18  Code  Gb  heap0.o [12]
__basic_malloc_intern   0x8001'52ed      0x94  Code  Lc  heap0.o [12]
__cmain                 0x7000'28cd            Code  Gb  cmain.o [14]
__data_GetMemChunk      0x8001'58e5      0x2c  Code  Gb  xgetmemchunk.o [12]
__data_GetMemChunk::start
                        0x2002'0fec       0x4  Data  Lc  xgetmemchunk.o [12]
__exit                  0x7000'2641      0x14  Code  Gb  exit.o [15]
__iar_Exp64             0x8001'5415     0x250  Code  Gb  iar_Exp64.o [13]
__iar_Memset            0x8001'4815            Code  Gb  ABImemset.o [14]
__iar_Memset_word       0x8001'481d            Code  Gb  ABImemset.o [14]
__iar_asin64            0x8000'0869            Code  Gb  asin.o [13]
__iar_atan2_32          0x8001'5229            Code  Gb  atan2f.o [13]
__iar_atan2_64          0x8001'51a9            Code  Gb  atan2.o [13]
__iar_data_init3        0x7000'2821      0x28  Code  Gb  data_init.o [14]
__iar_frexp             0x8001'56ed            Code  Gb  frexp.o [13]
__iar_frexp64           0x8001'56d5            Code  Gb  frexp.o [13]
__iar_frexpl            0x8001'56ed            Code  Gb  frexp.o [13]
__iar_init_vfp          0x7000'2849            Code  Gb  fpinit_M.o [13]
__iar_ldexp64           0x8001'5751            Code  Gb  ldexp.o [13]
__iar_lz77_init3        0x7000'24bb     0x17e  Code  Gb  lz77_init.o [14]
__iar_modf              0x8001'4bbd            Code  Gb  modf.o [13]
__iar_modf64            0x8001'4bad            Code  Gb  modf.o [13]
__iar_modfl             0x8001'4bbd            Code  Gb  modf.o [13]
__iar_pow64             0x8001'4c4d     0x4c0  Code  Gb  pow64.o [13]
__iar_pow_medium        0x8001'4c4d     0x4c0  Code  Gb  pow64.o [13]
__iar_pow_medium64      0x8001'4c4d     0x4c0  Code  Gb  pow64.o [13]
__iar_pow_mediuml       0x8001'4c4d     0x4c0  Code  Gb  pow64.o [13]
__iar_program_start     0x7000'28f1            Code  Gb  cstartup_M.o [14]
__iar_scalbln64         0x8001'5751            Code  Gb  ldexp.o [13]
__iar_scalbn64          0x8001'5751            Code  Gb  ldexp.o [13]
__iar_sqrt64            0x8001'474d            Code  Gb  sqrt.o [13]
__iar_vla_alloc2        0x8001'5ce1      0x12  Code  Gb  vla_alloc.o [12]
__iar_vla_dealloc2      0x8001'5cf3       0x4  Code  Gb  vla_alloc.o [12]
__iar_xatan             0x8000'08f1            Code  Gb  xatan.o [13]
__iar_xatanf            0x8001'5841            Code  Gb  xatanf.o [13]
__iar_zero_init3        0x7000'27e9      0x38  Code  Gb  zero_init3.o [14]
__low_level_init        0x7000'28eb       0x4  Code  Gb  low_level_init.o [12]
__vector_table          0x7000'2400            Data  Gb  vector_table_M.o [14]
__zf_vector_table       0x7000'2000     0x400  Data  Gb  zf_common_vector.o [8]
_call_main              0x7000'28d9            Code  Gb  cmain.o [14]
_exit                   0x8001'5919            Code  Gb  cexit.o [14]
abort                   0x7000'2639       0x6  Code  Gb  abort.o [12]
adjust_current_value    0x8000'b51f     0x140  Code  Gb  menu.o [1]
afio_init               0x8001'1c6f      0x3c  Code  Gb  zf_driver_gpio.o [11]
anglePID                0x2000'0b3c      0x1c  Data  Gb  zhuangtaiji.o [1]
angle_slope             0x2002'0d20       0x4  Data  Gb  xiangzi.o [1]
anglepia                0x2002'0e60       0x4  Data  Gb  xiangzi.o [1]
armPllConfig_BOARD_BootClockRUN
                        0x8000'0ab8       0x8  Data  Gb  clock_config.o [5]
arrow_smooth_factor     0x2000'0c48       0x4  Data  Lc  menu.o [1]
arrow_target_y          0x2000'0ee4       0x4  Data  Lc  menu.o [1]
arrow_y_position        0x2000'0ee0       0x4  Data  Lc  menu.o [1]
ascii_font_6x8          0x8000'280c     0x228  Data  Gb  zf_common_font.o [8]
ascii_font_8x16         0x8000'221c     0x5f0  Data  Gb  zf_common_font.o [8]
asin                    0x8000'0869            Code  Gb  asin.o [13]
asinl                   0x8000'0869            Code  Gb  asin.o [13]
atan2                   0x8001'51a9            Code  Gb  atan2.o [13]
atan2f                  0x8001'5229            Code  Gb  atan2f.o [13]
atan2l                  0x8001'51a9            Code  Gb  atan2.o [13]
b_d1                    0x2000'0c34       0x4  Data  Gb  menu.o [1]
b_d2                    0x2000'0c38       0x4  Data  Gb  menu.o [1]
b_d3                    0x2000'0c3c       0x4  Data  Gb  menu.o [1]
balinyu                 0x8000'c38d     0x2b4  Code  Gb  sxt.o [1]
binarizeImage           0x8000'c31d      0x40  Code  Gb  sxt.o [1]
boxAlignAnglePID        0x2000'0b58      0x1c  Data  Gb  zhuangtaiji.o [1]
box_detected            0x2002'0ce8       0x4  Data  Gb  uart.o [1]
button_handler          0x8000'b66d     0x250  Code  Gb  menu.o [1]
calculate_midline_offset
                        0x8000'd019      0xe4  Code  Gb  sxt.o [1]
camera_fifo_init        0x8000'fac9      0x10  Code  Gb  zf_device_camera.o [10]
camera_receiver_buffer  0x2002'0f4c       0x8  Data  Gb  zf_device_camera.o [10]
camera_receiver_fifo    0x2002'0f34      0x18  Data  Gb  zf_device_camera.o [10]
camera_type             0x2002'1027       0x1  Data  Gb  zf_device_type.o [10]
camera_uart_handler     0x2000'0c7c       0x4  Data  Gb  zf_device_type.o [10]
chinese                 0x8000'1b18     0x500  Data  Gb  hanzi.o [1]
class                   0x2002'0d14       0x4  Data  Gb  xiangzi.o [1]
class1                  0x2002'0d18       0x4  Data  Gb  xiangzi.o [1]
classChinese            0x8000'2018      0x20  Data  Gb  hanzi.o [1]
classIndex              0x2002'0d30       0x4  Data  Gb  xiangzi.o [1]
classValues             0x2002'0d34     0x12c  Data  Gb  xiangzi.o [1]
clock_init              0x8000'ec1d      0x20  Code  Gb  zf_common_clock.o [8]
count_get               0x8000'5555      0x42  Code  Gb  ce.o [1]
csi_add_empty_buffer    0x8001'1307      0x12  Code  Gb  zf_driver_csi.o [11]
csi_get_full_buffer     0x8001'1319      0x1c  Code  Gb  zf_driver_csi.o [11]
csi_handle              0x2002'0f58      0x38  Data  Gb  zf_driver_csi.o [11]
csi_init                0x8001'1343     0x15e  Code  Gb  zf_driver_csi.o [11]
csi_iomuxc              0x8001'11af     0x158  Code  Gb  zf_driver_csi.o [11]
csi_start               0x8001'1335       0xe  Code  Gb  zf_driver_csi.o [11]
current_level           0x2000'0edc       0x4  Data  Lc  menu.o [1]
dcd_data                0x7000'1030     0x410  Data  Gb  evkmimxrt1064_sdram_ini_dcd.o [5]
debug_assert_handler    0x8000'f057      0x46  Code  Gb  zf_common_debug.o [8]
debug_assert_handler{1}{2}::assert_nest_index
                        0x2002'1025       0x1  Data  Lc  zf_common_debug.o [8]
debug_delay             0x8000'ec3d      0x38  Code  Lc  zf_common_debug.o [8]
debug_init              0x8000'f10f      0x3a  Code  Gb  zf_common_debug.o [8]
debug_interrupr_handler
                        0x8000'f02f      0x28  Code  Gb  zf_common_debug.o [8]
debug_log_handler       0x8000'f09d      0x28  Code  Gb  zf_common_debug.o [8]
debug_output            0x8000'ed27     0x308  Code  Lc  zf_common_debug.o [8]
debug_output_info       0x2002'0f1c      0x14  Data  Lc  zf_common_debug.o [8]
debug_output_init       0x8000'f0e5      0x2a  Code  Gb  zf_common_debug.o [8]
debug_output_struct_init
                        0x8000'f0c5      0x20  Code  Gb  zf_common_debug.o [8]
debug_protective_handler
                        0x8000'ec75      0xa4  Code  Lc  zf_common_debug.o [8]
debug_uart_buffer       0x2002'0ec4      0x40  Data  Gb  zf_common_debug.o [8]
debug_uart_data         0x2002'1023       0x1  Data  Gb  zf_common_debug.o [8]
debug_uart_fifo         0x2002'0f04      0x18  Data  Gb  zf_common_debug.o [8]
debug_uart_str_output   0x8000'ed19       0xe  Code  Lc  zf_common_debug.o [8]
detected_side           0x2000'0c74       0x4  Data  Lc  xiangzi.o [1]
direction_l             0x2002'0ca8       0x4  Data  Gb  sxt.o [1]
direction_r             0x2002'0cac       0x4  Data  Gb  sxt.o [1]
display_menu            0x8000'b473      0xac  Code  Gb  menu.o [1]
distancePID             0x2000'0b04      0x1c  Data  Gb  zhuangtaiji.o [1]
distance_cleared_for_phase1
                        0x2002'101d       0x1  Data  Lc  xiangzi.o [1]
distance_mm             0x2002'0cec       0x4  Data  Gb  uart.o [1]
draw_new_edge_lines     0x8000'cdb1     0x24e  Code  Gb  sxt.o [1]
dt                      0x8000'0a1c       0x4  Data  Gb  ce.o [1]
dx                      0x2000'0c14      0x10  Data  Gb  sxt.o [1]
dy                      0x2000'0c04      0x10  Data  Gb  sxt.o [1]
encoder_clear_count     0x8001'19d5      0x2e  Code  Gb  zf_driver_encoder.o [11]
encoder_dir_init        0x8001'1bc5      0x1a  Code  Gb  zf_driver_encoder.o [11]
encoder_get_count       0x8001'19a5      0x30  Code  Gb  zf_driver_encoder.o [11]
encoder_quad_init       0x8001'1a03      0xfa  Code  Gb  zf_driver_encoder.o [11]
eulerAngle_pitch        0x2000'0f04       0x4  Data  Gb  siyuanshu.o [1]
eulerAngle_roll         0x2000'0f08       0x4  Data  Gb  siyuanshu.o [1]
eulerAngle_yaw          0x2000'0f0c       0x4  Data  Gb  siyuanshu.o [1]
exit                    0x8001'5d01       0x4  Code  Gb  exit.o [12]
fflag                   0x2002'0ec0       0x4  Data  Gb  yuansu.o [1]
fifo_clear              0x8000'f1d9      0x9e  Code  Gb  zf_common_fifo.o [8]
fifo_end_offset         0x8000'f1b1      0x28  Code  Lc  zf_common_fifo.o [8]
fifo_head_offset        0x8000'f189      0x28  Code  Lc  zf_common_fifo.o [8]
fifo_init               0x8000'f763      0x42  Code  Gb  zf_common_fifo.o [8]
fifo_read_buffer        0x8000'f53b     0x228  Code  Gb  zf_common_fifo.o [8]
fifo_used               0x8000'f277      0x26  Code  Gb  zf_common_fifo.o [8]
fifo_write_buffer       0x8000'f335     0x206  Code  Gb  zf_common_fifo.o [8]
fifo_write_element      0x8000'f29d      0x98  Code  Gb  zf_common_fifo.o [8]
filter_lines            0x8000'cc8d     0x10a  Code  Gb  sxt.o [1]
find_edge_line          0x8000'cb51     0x130  Code  Gb  sxt.o [1]
flag_finish             0x2002'0e84       0x4  Data  Gb  xiangzi.o [1]
flexio_camera_vsync_handler
                        0x2000'0c80       0x4  Data  Gb  zf_device_type.o [10]
free                    0x8001'5cd5       0x4  Code  Gb  heaptramp0.o [12]
frexp                   0x8001'56d5            Code  Gb  frexp.o [13]
frexpl                  0x8001'56d5            Code  Gb  frexp.o [13]
func_double_to_str      0x8000'f835     0x19a  Code  Gb  zf_common_function.o [8]
func_int_to_str         0x8000'f7ad      0x88  Code  Gb  zf_common_function.o [8]
g_boot_data             0x7000'1020      0x10  Data  Gb  fsl_flexspi_nor_boot.o [5]
g_current_angular_rate  0x2000'0e94       0x4  Data  Gb  isr.o [7]
g_lpspiDummyData        0x2000'0e1c       0x8  Data  Gb  fsl_lpspi.o [6]
g_rtcXtalFreq           0x2000'0cd4       0x4  Data  Gb  fsl_clock.o [6]
g_target_angular_velocity_setpoint
                        0x2002'0fac       0x4  Data  Gb  zhuangtaiji.o [1]
g_xtalFreq              0x2000'0cd0       0x4  Data  Gb  fsl_clock.o [6]
get_image_sum_real      0x8000'c641      0x26  Code  Gb  sxt.o [1]
gpio_get_level          0x8001'1dab      0x1e  Code  Gb  zf_driver_gpio.o [11]
gpio_init               0x8001'1e2f      0x6a  Code  Gb  zf_driver_gpio.o [11]
gpio_iomuxc             0x8001'1cab      0xbe  Code  Gb  zf_driver_gpio.o [11]
gpio_set_dir            0x8001'1dc9      0x66  Code  Gb  zf_driver_gpio.o [11]
gpio_set_level          0x8001'1d69      0x42  Code  Gb  zf_driver_gpio.o [11]
gx_offset               0x2000'0f10       0x4  Data  Gb  siyuanshu.o [1]
gy_offset               0x2000'0f14       0x4  Data  Gb  siyuanshu.o [1]
gyroOffsetInit          0x8000'b92d      0xce  Code  Gb  siyuanshu.o [1]
gz_offset               0x2000'0f18       0x4  Data  Gb  siyuanshu.o [1]
hasReceivedFeedback     0x2002'1021       0x1  Data  Lc  xiangzi.o [1]
hasRecordedResult       0x2002'101b       0x1  Data  Lc  xiangzi.o [1]
icmAHRSupdate           0x8000'bba1     0x35e  Code  Gb  siyuanshu.o [1]
icmGetValues            0x8000'ba31     0x152  Code  Gb  siyuanshu.o [1]
icm_ki                  0x2000'0c54       0x4  Data  Gb  siyuanshu.o [1]
icm_kp                  0x2000'0c50       0x4  Data  Gb  siyuanshu.o [1]
image_vector_table      0x7000'1000      0x20  Data  Gb  fsl_flexspi_nor_boot.o [5]
imu660ra_acc_x          0x2002'1006       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_acc_y          0x2002'1008       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_acc_z          0x2002'100a       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_config_file    0x8000'31ec    0x2000  Data  Gb  zf_device_config.o [16]
imu660ra_euler_show     0x8000'bf05      0xac  Code  Gb  siyuanshu.o [1]
imu660ra_get_acc        0x8000'fc15      0x3e  Code  Gb  zf_device_imu660ra.o [10]
imu660ra_get_gyro       0x8000'fc53      0x3e  Code  Gb  zf_device_imu660ra.o [10]
imu660ra_gyro_x         0x2002'1000       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_gyro_y         0x2002'1002       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_gyro_z         0x2002'1004       0x2  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_init           0x8000'fc91      0xd6  Code  Gb  zf_device_imu660ra.o [10]
imu660ra_read_register  0x8000'fb6b      0x30  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_read_registers
                        0x8000'fb9b      0x44  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_self_check     0x8000'fbdf      0x36  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_transition_factor
                        0x2000'0c24       0x8  Data  Gb  zf_device_imu660ra.o [10]
imu660ra_write_register
                        0x8000'fb11      0x2c  Code  Lc  zf_device_imu660ra.o [10]
imu660ra_write_registers
                        0x8000'fb3d      0x2e  Code  Lc  zf_device_imu660ra.o [10]
inTextMode              0x2002'101a       0x1  Data  Gb  xiangzi.o [1]
init_other_menus        0x8000'b34d      0x4e  Code  Gb  menu.o [1]
init_pid_menu           0x8000'b2cd      0x80  Code  Gb  menu.o [1]
interrupt_enable        0x8000'fa9d       0xe  Code  Gb  zf_common_interrupt.o [8]
interrupt_global_disable
                        0x8000'fa71      0x10  Code  Gb  zf_common_interrupt.o [8]
interrupt_init          0x8000'fabf       0xa  Code  Gb  zf_common_interrupt.o [8]
interrupt_nest_count    0x2002'0f30       0x4  Data  Lc  zf_common_interrupt.o [8]
interrupt_set_priority  0x8000'faab      0x14  Code  Gb  zf_common_interrupt.o [8]
invSqrt                 0x8000'b9fb      0x36  Code  Gb  siyuanshu.o [1]
inverse_perspective_image
                        0x2001'3b34    0x4b00  Data  Gb  sxt.o [1]
isBoxDetectedEntry      0x2000'0c97       0x1  Data  Lc  xiangzi.o [1]
isInFineTuning          0x2002'101c       0x1  Data  Lc  xiangzi.o [1]
last_push_time          0x2002'0d28       0x4  Data  Gb  xiangzi.o [1]
last_side_detect_time   0x2002'0d2c       0x4  Data  Gb  xiangzi.o [1]
ldexp                   0x8001'5751            Code  Gb  ldexp.o [13]
ldexpl                  0x8001'5751            Code  Gb  ldexp.o [13]
left                    0x2002'0e98       0x4  Data  Gb  xiangzi.o [1]
left_corner             0x2002'05c8     0x190  Data  Gb  sxt.o [1]
left_error_sum          0x2002'0cb0       0x4  Data  Gb  sxt.o [1]
left_lenth              0x2002'05b4       0x4  Data  Gb  sxt.o [1]
left_line_raw           0x2001'd6d4     0xfa0  Data  Gb  sxt.o [1]
left_lose               0x2002'0cc0       0x4  Data  Gb  sxt.o [1]
left_start              0x2002'05bc       0x4  Data  Gb  sxt.o [1]
left_straight           0x2002'0cb8       0x4  Data  Gb  sxt.o [1]
left_width              0x2002'08e8     0x1e0  Data  Gb  sxt.o [1]
line_grow_left          0x8000'c78b     0x1dc  Code  Gb  sxt.o [1]
line_grow_right         0x8000'c967     0x1dc  Code  Gb  sxt.o [1]
line_init               0x8000'c669     0x122  Code  Gb  sxt.o [1]
lnbias                  0x8000'0448     0x420  Data  Lc  pow64.o [13]
low1                    0x2000'0c44       0x4  Data  Gb  menu.o [1]
m_boot_hdr_conf_start {Abs}
                        0x7000'0000            Data  Gb  <internal module>
main                    0x8000'acdd     0x5f0  Code  Gb  main.o [7]
main::prev_b16_level    0x2000'0c94       0x1  Data  Lc  main.o [7]
main::prev_b17_level    0x2000'0c95       0x1  Data  Lc  main.o [7]
main::prev_c18_level    0x2000'0c92       0x1  Data  Lc  main.o [7]
main::prev_c19_level    0x2000'0c93       0x1  Data  Lc  main.o [7]
main::prev_special_mode
                        0x2000'0c2c       0x4  Data  Lc  main.o [7]
main{1}{2}::turn_off_time
                        0x2000'0ea4       0x4  Data  Lc  main.o [7]
main{1}{2}{4}{5}{6}::current_page
                        0x2002'1012       0x1  Data  Lc  main.o [7]
main{1}{2}{4}{5}{6}::page_switch_time
                        0x2000'0ea0       0x4  Data  Lc  main.o [7]
menu_init               0x8000'b39b      0x42  Code  Gb  menu.o [1]
menu_length_stack       0x2000'0ebc      0x10  Data  Lc  menu.o [1]
menu_level1             0x2000'0100      0xe0  Data  Gb  menu.o [1]
menu_level1_length      0x8000'212c       0x4  Data  Gb  menu.o [1]
menu_level2_fifth       0x2000'02e0      0x80  Data  Gb  menu.o [1]
menu_level2_first       0x2000'01e0      0x80  Data  Gb  menu.o [1]
menu_level2_fourth      0x2000'0960      0x60  Data  Gb  menu.o [1]
menu_level2_seventh     0x2000'03e0      0x80  Data  Gb  menu.o [1]
menu_level2_sixth       0x2000'0360      0x80  Data  Gb  menu.o [1]
menu_level2_third       0x2000'0260      0x80  Data  Gb  menu.o [1]
menu_pid_angle_params   0x2000'06e0      0x80  Data  Gb  menu.o [1]
menu_pid_distance_params
                        0x2000'0660      0x80  Data  Gb  menu.o [1]
menu_pid_level2         0x2000'0000     0x100  Data  Gb  menu.o [1]
menu_pid_qh_params      0x2000'07e0      0x80  Data  Gb  menu.o [1]
menu_pid_saoxian_params
                        0x2000'0860      0x80  Data  Gb  menu.o [1]
menu_pid_saoxian_params_h
                        0x2000'08e0      0x80  Data  Gb  menu.o [1]
menu_pid_wheel0_params  0x2000'04e0      0x80  Data  Gb  menu.o [1]
menu_pid_wheel1_params  0x2000'0560      0x80  Data  Gb  menu.o [1]
menu_pid_wheel2_params  0x2000'05e0      0x80  Data  Gb  menu.o [1]
menu_pid_wheel_level3   0x2000'0460      0x80  Data  Gb  menu.o [1]
menu_pid_zy_params      0x2000'0760      0x80  Data  Gb  menu.o [1]
menu_stack              0x2000'0eac      0x10  Data  Lc  menu.o [1]
mid_angle_offset        0x2002'0cd4       0x4  Data  Gb  sxt.o [1]
mid_lenth               0x2002'05c4       0x4  Data  Gb  sxt.o [1]
mid_line_raw            0x2001'f614     0xfa0  Data  Gb  sxt.o [1]
mid_lose                0x2002'0cc8       0x4  Data  Gb  sxt.o [1]
mid_x_offset            0x2002'0ccc       0x4  Data  Gb  sxt.o [1]
mid_y_offset            0x2002'0cd0       0x4  Data  Gb  sxt.o [1]
modf                    0x8001'4bad            Code  Gb  modf.o [13]
modfl                   0x8001'4bad            Code  Gb  modf.o [13]
mt9v03x_finish_flag     0x2002'1026       0x1  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_finished_callback
                        0x8000'ffbd      0x4e  Code  Gb  zf_device_mt9v03x.o [10]
mt9v03x_get_config      0x8000'fe4f      0xce  Code  Lc  zf_device_mt9v03x.o [10]
mt9v03x_get_confing_buffer
                        0x2000'0abc      0x24  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_get_version     0x8000'ff1d      0x76  Code  Gb  zf_device_mt9v03x.o [10]
mt9v03x_h_blank         0x2002'0fe0       0x4  Data  Lc  zf_device_config.o [16]
mt9v03x_iic_inf_struct  0x2002'0fcc      0x10  Data  Lc  zf_device_config.o [16]
mt9v03x_image           0x2002'0f54       0x4  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_image1          0x2002'1040    0x4b00  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_image2          0x2002'5b40    0x4b00  Data  Gb  zf_device_mt9v03x.o [10]
mt9v03x_image_2         0x2001'8634    0x4b00  Data  Gb  sxt.o [1]
mt9v03x_init            0x8001'000b     0x10e  Code  Gb  zf_device_mt9v03x.o [10]
mt9v03x_init_config     0x2002'0fb8      0x14  Data  Lc  zf_device_config.o [16]
mt9v03x_read_word_sccb  0x8001'40cb      0x28  Code  Lc  zf_device_config.o [16]
mt9v03x_set_config      0x8000'fda1      0xae  Code  Lc  zf_device_mt9v03x.o [10]
mt9v03x_set_config_sccb
                        0x8001'40f3     0x646  Code  Gb  zf_device_config.o [16]
mt9v03x_set_confing_buffer
                        0x2000'0a94      0x28  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_type            0x2000'0c99       0x1  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_uart_callback   0x8000'ff93      0x2a  Code  Lc  zf_device_mt9v03x.o [10]
mt9v03x_v_blank         0x2002'0fdc       0x4  Data  Lc  zf_device_config.o [16]
mt9v03x_version         0x2002'100c       0x2  Data  Lc  zf_device_mt9v03x.o [10]
mt9v03x_write_word_sccb
                        0x8001'40a1      0x2a  Code  Lc  zf_device_config.o [16]
number                  0x2000'0cc0       0x4  Data  Gb  ce.o [1]
omegaMeasured           0x2000'0ca8       0xc  Data  Gb  ce.o [1]
omegaRef                0x2000'0cb4       0xc  Data  Gb  ce.o [1]
otsuThreshold           0x8000'c195     0x176  Code  Gb  sxt.o [1]
otsu_erzhihua           0x8000'c35d      0x26  Code  Gb  sxt.o [1]
out                     0x8001'5d05      0x18  Code  Lc  xprintftiny.o [12]
phase                   0x2002'0e64       0x4  Data  Gb  xiangzi.o [1]
phase_transition_angle  0x2002'0d24       0x4  Data  Gb  xiangzi.o [1]
pia                     0x2002'0ce4       0x4  Data  Gb  uart.o [1]
pia1                    0x2002'0d0c       0x4  Data  Gb  uart.o [1]
pit_init                0x8001'1fd3      0x68  Code  Gb  zf_driver_pit.o [11]
pit_init::init_flag     0x2002'1028       0x1  Data  Lc  zf_driver_pit.o [11]
position_stable_time    0x2000'0c60       0x4  Data  Gb  xiangzi.o [1]
pow                     0x8001'4c4d     0x4c0  Code  Gb  pow64.o [13]
powl                    0x8001'4c4d     0x4c0  Code  Gb  pow64.o [13]
push                    0x8000'dead     0x744  Code  Gb  xiangzi.o [1]
push::angle_condition_start_time
                        0x2002'0e7c       0x4  Data  Lc  xiangzi.o [1]
push::cleared_ti        0x2002'101e       0x1  Data  Lc  xiangzi.o [1]
push::distance_at_yyy_1
                        0x2002'0e80       0x4  Data  Lc  xiangzi.o [1]
push::first_call        0x2000'0c96       0x1  Data  Lc  xiangzi.o [1]
push::in_transition     0x2002'101f       0x1  Data  Lc  xiangzi.o [1]
push::phase3_align_start_time
                        0x2002'0e70       0x4  Data  Lc  xiangzi.o [1]
push::phase3_timeout_active
                        0x2002'1020       0x1  Data  Lc  xiangzi.o [1]
push::phase_transition_start_time
                        0x2002'0e6c       0x4  Data  Lc  xiangzi.o [1]
push::saved_class1      0x2002'0e68       0x4  Data  Lc  xiangzi.o [1]
push::yyy_0_start_time  0x2002'0e78       0x4  Data  Lc  xiangzi.o [1]
push::yyy_1_start_time  0x2002'0e74       0x4  Data  Lc  xiangzi.o [1]
pwm_init                0x8001'25fd     0x222  Code  Gb  zf_driver_pwm.o [11]
pwm_iomuxc              0x8001'20a1     0x49a  Code  Gb  zf_driver_pwm.o [11]
pwm_set_duty            0x8001'253b      0xc2  Code  Gb  zf_driver_pwm.o [11]
qhPID                   0x2000'0b74      0x1c  Data  Gb  zhuangtaiji.o [1]
qspiflash_config        0x7000'0000     0x200  Data  Gb  evkmimxrt1064_flexspi_nor_config.o [5]
qtimer_index            0x2000'0bc8      0x14  Data  Gb  zf_driver_encoder.o [11]
qtimer_iomuxc           0x8001'1651     0x354  Code  Gb  zf_driver_encoder.o [11]
record_detection_result
                        0x8000'de15      0x98  Code  Gb  xiangzi.o [1]
recorded_slope          0x2002'0d1c       0x4  Data  Gb  xiangzi.o [1]
right                   0x2002'0e9c       0x4  Data  Gb  xiangzi.o [1]
right_corner            0x2002'0758     0x190  Data  Gb  sxt.o [1]
right_error_sum         0x2002'0cb4       0x4  Data  Gb  sxt.o [1]
right_lenth             0x2002'05b8       0x4  Data  Gb  sxt.o [1]
right_line_raw          0x2001'e674     0xfa0  Data  Gb  sxt.o [1]
right_lose              0x2002'0cc4       0x4  Data  Gb  sxt.o [1]
right_start             0x2002'05c0       0x4  Data  Gb  sxt.o [1]
right_straight          0x2002'0cbc       0x4  Data  Gb  sxt.o [1]
right_width             0x2002'0ac8     0x1e0  Data  Gb  sxt.o [1]
robot_x                 0x2000'0cc4       0x4  Data  Gb  ce.o [1]
robot_y                 0x2000'0cc8       0x4  Data  Gb  ce.o [1]
rt1064_storageU1        0x8000'd2f3      0x44  Code  Gb  uart.o [1]
rt1064_storageU2        0x8000'd489      0x82  Code  Gb  uart.o [1]
rt1064_storageU4        0x8000'd653      0x26  Code  Gb  uart.o [1]
rx_art1                 0x2002'0cdc       0x8  Data  Gb  uart.o [1]
rx_art2                 0x2002'0cf4       0x8  Data  Gb  uart.o [1]
rx_art4                 0x2002'0d00       0x8  Data  Gb  uart.o [1]
rx_data1                0x2002'1014       0x1  Data  Gb  uart.o [1]
rx_data2                0x2002'1016       0x1  Data  Gb  uart.o [1]
rx_data4                0x2002'1018       0x1  Data  Gb  uart.o [1]
s_EDMAHandle            0x2000'0cf4      0x80  Data  Lc  fsl_edma.o [6]
s_ENETHandle            0x2000'0d74       0x8  Data  Lc  fsl_enet.o [6]
s_baudratePrescaler     0x8000'1408       0x8  Data  Lc  fsl_lpspi.o [6]
s_cdcVcom               0x2002'0f9c      0x10  Data  Gb  zf_driver_usb_cdc.o [11]
s_csiBases              0x8000'0cc0       0x4  Data  Lc  fsl_csi.o [6]
s_csiClocks             0x8001'5922       0x2  Data  Lc  fsl_csi.o [6]
s_csiHandle             0x2000'0cd8       0x4  Data  Lc  fsl_csi.o [6]
s_csiIRQ                0x8001'5924       0x2  Data  Lc  fsl_csi.o [6]
s_csiIsr                0x2000'0cdc       0x4  Data  Lc  fsl_csi.o [6]
s_debugConsole          0x2000'0ce0      0x14  Data  Lc  fsl_debug_console.o [5]
s_enet1588TimerIsr      0x2000'0d9c       0x8  Data  Lc  fsl_enet.o [6]
s_enetBases             0x8000'0e08       0x8  Data  Lc  fsl_enet.o [6]
s_enetErrIsr            0x2000'0d8c       0x8  Data  Lc  fsl_enet.o [6]
s_enetRxIsr             0x2000'0d84       0x8  Data  Lc  fsl_enet.o [6]
s_enetTsIsr             0x2000'0d94       0x8  Data  Lc  fsl_enet.o [6]
s_enetTxIsr             0x2000'0d7c       0x8  Data  Lc  fsl_enet.o [6]
s_flexcanHandle         0x2000'0da4      0x10  Data  Lc  fsl_flexcan.o [6]
s_flexcanIsr            0x2000'0db4       0x4  Data  Lc  fsl_flexcan.o [6]
s_flexioHandle          0x2000'0db8       0x8  Data  Lc  fsl_flexio.o [6]
s_flexioIsr             0x2000'0dc8       0x8  Data  Lc  fsl_flexio.o [6]
s_flexioType            0x2000'0dc0       0x8  Data  Lc  fsl_flexio.o [6]
s_gpioBases             0x8000'1004      0x2c  Data  Lc  fsl_gpio.o [6]
s_gpioClock             0x8000'1030       0xc  Data  Lc  fsl_gpio.o [6]
s_gptBases              0x8000'1210       0xc  Data  Lc  fsl_gpt.o [6]
s_gptClocks             0x8000'121c       0x8  Data  Lc  fsl_gpt.o [6]
s_lpi2cMasterHandle     0x2000'0dd4      0x14  Data  Gb  fsl_lpi2c.o [6]
s_lpi2cMasterIsr        0x2000'0dd0       0x4  Data  Gb  fsl_lpi2c.o [6]
s_lpi2cSlaveHandle      0x2000'0dec      0x14  Data  Lc  fsl_lpi2c.o [6]
s_lpi2cSlaveIsr         0x2000'0de8       0x4  Data  Lc  fsl_lpi2c.o [6]
s_lpspiBases            0x8000'1410      0x14  Data  Lc  fsl_lpspi.o [6]
s_lpspiClocks           0x8000'1424       0xc  Data  Lc  fsl_lpspi.o [6]
s_lpspiHandle           0x2000'0e00      0x14  Data  Lc  fsl_lpspi.o [6]
s_lpspiMasterIsr        0x2000'0e14       0x4  Data  Lc  fsl_lpspi.o [6]
s_lpspiSlaveIsr         0x2000'0e18       0x4  Data  Lc  fsl_lpspi.o [6]
s_lpuartBases           0x8000'15e4      0x24  Data  Lc  fsl_lpuart.o [6]
s_lpuartClock           0x8000'1608      0x14  Data  Lc  fsl_lpuart.o [6]
s_lpuartHandle          0x2000'0e24      0x24  Data  Gb  fsl_lpuart.o [6]
s_lpuartIsr             0x2000'0e48       0x4  Data  Gb  fsl_lpuart.o [6]
s_pitBases              0x8000'1754       0x4  Data  Lc  fsl_pit.o [6]
s_pitClocks             0x8001'5926       0x2  Data  Lc  fsl_pit.o [6]
s_pwmBases              0x8000'18d8      0x14  Data  Lc  fsl_pwm.o [6]
s_pwmClocks             0x8000'18ec      0x28  Data  Lc  fsl_pwm.o [6]
s_qtmrBases             0x8000'1a4c      0x14  Data  Lc  fsl_qtmr.o [6]
s_qtmrClocks            0x8000'1a60       0xc  Data  Lc  fsl_qtmr.o [6]
s_saiHandle             0x2000'0e4c      0x20  Data  Lc  fsl_sai.o [6]
s_saiRxIsr              0x2000'0e70       0x4  Data  Lc  fsl_sai.o [6]
s_saiTxIsr              0x2000'0e6c       0x4  Data  Lc  fsl_sai.o [6]
s_spdifHandle           0x2000'0e74       0x8  Data  Lc  fsl_spdif.o [6]
s_spdifRxIsr            0x2000'0e80       0x4  Data  Lc  fsl_spdif.o [6]
s_spdifTxIsr            0x2000'0e7c       0x4  Data  Lc  fsl_spdif.o [6]
s_usdhcBase             0x8000'1b0c       0xc  Data  Lc  fsl_usdhc.o [6]
s_usdhcHandle           0x2000'0e84       0xc  Data  Lc  fsl_usdhc.o [6]
s_usdhcIsr              0x2000'0e90       0x4  Data  Lc  fsl_usdhc.o [6]
safe                    0x2000'0c70       0x4  Data  Gb  xiangzi.o [1]
saoxianPID              0x2000'0b90      0x1c  Data  Gb  zhuangtaiji.o [1]
saoxianPIDh             0x2000'0bac      0x1c  Data  Gb  zhuangtaiji.o [1]
scalbln                 0x8001'5751            Code  Gb  ldexp.o [13]
scalblnl                0x8001'5751            Code  Gb  ldexp.o [13]
scalbn                  0x8001'5751            Code  Gb  ldexp.o [13]
scalbnl                 0x8001'5751            Code  Gb  ldexp.o [13]
selected_index_stack    0x2000'0ecc      0x10  Data  Lc  menu.o [1]
set_camera_type         0x8001'10b7      0x1c  Code  Gb  zf_device_type.o [10]
set_target_point_index  0x8000'd101       0xa  Code  Gb  sxt.o [1]
side_detected           0x2002'1022       0x1  Data  Lc  xiangzi.o [1]
side_detection_start_time
                        0x2002'0ea0       0x4  Data  Lc  xiangzi.o [1]
slope                   0x2002'0d10       0x4  Data  Gb  uart.o [1]
slope_raw               0x2002'0e88       0x4  Data  Gb  xiangzi.o [1]
soft_iic_init           0x8001'2f39      0x7a  Code  Gb  zf_driver_soft_iic.o [11]
soft_iic_read_data      0x8001'2d5d     0x11c  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_sccb_read_register
                        0x8001'2ecd      0x6c  Code  Gb  zf_driver_soft_iic.o [11]
soft_iic_sccb_write_register
                        0x8001'2e81      0x4c  Code  Gb  zf_driver_soft_iic.o [11]
soft_iic_send_ack       0x8001'2ad5      0xce  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_send_data      0x8001'2c77      0xe6  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_start          0x8001'298d      0xa4  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_stop           0x8001'2a31      0xa4  Code  Lc  zf_driver_soft_iic.o [11]
soft_iic_wait_ack       0x8001'2ba3      0xd4  Code  Lc  zf_driver_soft_iic.o [11]
special_mode            0x2000'0ee8       0x4  Data  Gb  menu.o [1]
special_mode_4_angle    0x2000'0e9c       0x4  Data  Lc  isr.o [7]
special_mode_4_entry    0x2002'1010       0x1  Data  Lc  isr.o [7]
special_mode_4_initialized
                        0x2002'1011       0x1  Data  Lc  isr.o [7]
special_mode_4_timer    0x2000'0e98       0x4  Data  Lc  isr.o [7]
spi_cs_index            0x2002'0f90       0xc  Data  Lc  zf_driver_spi.o [11]
spi_index               0x2000'0bf0      0x14  Data  Lc  zf_driver_spi.o [11]
spi_init                0x8001'3761     0x23c  Code  Gb  zf_driver_spi.o [11]
spi_iomuxc              0x8001'30cf     0x430  Code  Gb  zf_driver_spi.o [11]
spi_read_8bit_registers
                        0x8001'3705      0x50  Code  Gb  zf_driver_spi.o [11]
spi_write               0x8001'34ff      0xfc  Code  Gb  zf_driver_spi.o [11]
spi_write_16bit         0x8001'362d      0x1e  Code  Gb  zf_driver_spi.o [11]
spi_write_16bit_array   0x8001'364b      0x36  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit          0x8001'361b      0x12  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit_array    0x8001'35fb      0x20  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit_register
                        0x8001'3681      0x34  Code  Gb  zf_driver_spi.o [11]
spi_write_8bit_registers
                        0x8001'36b5      0x50  Code  Gb  zf_driver_spi.o [11]
sprintf                 0x8001'5295      0x40  Code  Gb  sprintf.o [12]
sqrt                    0x8001'474d            Code  Gb  sqrt.o [13]
sqrtl                   0x8001'474d            Code  Gb  sqrt.o [13]
storageU1               0x2002'0cd8       0x4  Data  Gb  uart.o [1]
storageU2               0x2002'0cf0       0x4  Data  Gb  uart.o [1]
storageU4               0x2002'0cfc       0x4  Data  Gb  uart.o [1]
strcmp                  0x8001'5195            Code  Gb  strcmp.o [14]
strlen                  0x8001'515d            Code  Gb  strlen.o [14]
system_clock            0x2000'0c78       0x4  Data  Gb  zf_common_clock.o [8]
system_delay_init       0x8001'15e9      0x2c  Code  Gb  zf_driver_delay.o [11]
system_delay_ms         0x8001'15ab      0x3e  Code  Gb  zf_driver_delay.o [11]
target_lr               0x2002'0e90       0x4  Data  Gb  xiangzi.o [1]
target_point_index      0x2000'0c58       0x4  Data  Gb  sxt.o [1]
target_qh               0x2002'0e8c       0x4  Data  Gb  xiangzi.o [1]
tft180_bgcolor          0x2002'100e       0x2  Data  Lc  zf_device_tft180.o [10]
tft180_clear            0x8001'036d      0x9a  Code  Gb  zf_device_tft180.o [10]
tft180_debug_init       0x8001'0303      0x6a  Code  Lc  zf_device_tft180.o [10]
tft180_display_dir      0x2000'0c9a       0x1  Data  Lc  zf_device_tft180.o [10]
tft180_display_font     0x2000'0c9b       0x1  Data  Lc  zf_device_tft180.o [10]
tft180_draw_point       0x8001'0451      0x88  Code  Gb  zf_device_tft180.o [10]
tft180_init             0x8001'0d2d     0x368  Code  Gb  zf_device_tft180.o [10]
tft180_pencolor         0x2000'0c90       0x2  Data  Lc  zf_device_tft180.o [10]
tft180_set_color        0x8001'0443       0xe  Code  Gb  zf_device_tft180.o [10]
tft180_set_dir          0x8001'0407      0x3c  Code  Gb  zf_device_tft180.o [10]
tft180_set_region       0x8001'01a9     0x15a  Code  Lc  zf_device_tft180.o [10]
tft180_show_char        0x8001'04db     0x228  Code  Gb  zf_device_tft180.o [10]
tft180_show_chinese     0x8001'0ba5     0x178  Code  Gb  zf_device_tft180.o [10]
tft180_show_float       0x8001'08af     0x15a  Code  Gb  zf_device_tft180.o [10]
tft180_show_gray_image  0x8001'0a25     0x178  Code  Gb  zf_device_tft180.o [10]
tft180_show_int         0x8001'07b5      0xfa  Code  Gb  zf_device_tft180.o [10]
tft180_show_string      0x8001'0705      0xb0  Code  Gb  zf_device_tft180.o [10]
tft180_write_index      0x8001'0185      0x24  Code  Lc  zf_device_tft180.o [10]
tft180_x_max            0x2000'0c9c       0x1  Data  Lc  zf_device_tft180.o [10]
tft180_y_max            0x2000'0c9d       0x1  Data  Lc  zf_device_tft180.o [10]
ti                      0x2000'0ca0       0x4  Data  Gb  ce.o [1]
tim                     0x2000'0ca4       0x4  Data  Gb  ce.o [1]
time1                   0x2000'0c64       0x4  Data  Gb  xiangzi.o [1]
time2                   0x2000'0c68       0x4  Data  Gb  xiangzi.o [1]
time3                   0x2000'0c6c       0x4  Data  Gb  xiangzi.o [1]
tof_module_exti_handler
                        0x2000'0c8c       0x4  Data  Gb  zf_device_type.o [10]
top1                    0x2000'0c40       0x4  Data  Gb  menu.o [1]
top_detected            0x2002'0d08       0x4  Data  Gb  uart.o [1]
total_distance          0x2000'0ccc       0x4  Data  Gb  ce.o [1]
type_default_callback   0x8001'10b5       0x2  Code  Gb  zf_device_type.o [10]
uart1_rx_art            0x8000'd1a5     0x14e  Code  Gb  uart.o [1]
uart1_rx_art::stage     0x2002'1015       0x1  Data  Lc  uart.o [1]
uart1_rx_interrupt_new_handler
                        0x8000'd18d      0x18  Code  Gb  uart.o [1]
uart2_rx_art            0x8000'd34f     0x13a  Code  Gb  uart.o [1]
uart2_rx_art::stage     0x2002'1017       0x1  Data  Lc  uart.o [1]
uart2_rx_interrupt_new_handler
                        0x8000'd337      0x18  Code  Gb  uart.o [1]
uart4_rx_art            0x8000'd521     0x132  Code  Gb  uart.o [1]
uart4_rx_art::stage     0x2002'1019       0x1  Data  Lc  uart.o [1]
uart4_rx_interrupt_new_handler
                        0x8000'd50b      0x16  Code  Gb  uart.o [1]
uart_index              0x2000'0ae0      0x24  Data  Lc  zf_driver_uart.o [11]
uart_init               0x8001'3f1b      0x8e  Code  Gb  zf_driver_uart.o [11]
uart_iomuxc             0x8001'3aeb     0x34a  Code  Gb  zf_driver_uart.o [11]
uart_query_byte         0x8001'3ea9      0x34  Code  Gb  zf_driver_uart.o [11]
uart_rx_interrupt       0x8001'3edd      0x3e  Code  Gb  zf_driver_uart.o [11]
uart_write_buffer       0x8001'3e5d      0x2a  Code  Gb  zf_driver_uart.o [11]
uart_write_byte         0x8001'3e35      0x28  Code  Gb  zf_driver_uart.o [11]
uart_write_string       0x8001'3e87      0x22  Code  Gb  zf_driver_uart.o [11]
update_arrow_animation  0x8000'b3dd      0x46  Code  Gb  menu.o [1]
update_inverse_perspective_image
                        0x8000'c029      0x3c  Code  Gb  sxt.o [1]
update_menu_animation   0x8000'b425      0x4e  Code  Gb  menu.o [1]
vx                      0x2002'0fb4       0x4  Data  Gb  zhuangtaiji.o [1]
vy                      0x2002'0fb0       0x4  Data  Gb  zhuangtaiji.o [1]
wheelPID                0x2000'09c0      0x54  Data  Gb  ce.o [1]
wheelPID_postPush       0x2000'0a14      0x54  Data  Gb  ce.o [1]
wireless_module_spi_handler
                        0x2000'0c88       0x4  Data  Gb  zf_device_type.o [10]
wireless_module_uart_handler
                        0x2000'0c84       0x4  Data  Gb  zf_device_type.o [10]
yyy                     0x2002'0e94       0x4  Data  Gb  xiangzi.o [1]
zf_debug_assert_enable  0x2000'0c98       0x1  Data  Lc  zf_common_debug.o [8]
zf_debug_init_flag      0x2002'1024       0x1  Data  Lc  zf_common_debug.o [8]
zong                    0x8000'e665     0x4ec  Code  Gb  xiangzi.o [1]
zong::initialPhaseStartTime
                        0x2002'0eb8       0x4  Data  Lc  xiangzi.o [1]
zong::positionStable    0x2002'0ea8       0x4  Data  Lc  xiangzi.o [1]
zong::positionStableStartTime
                        0x2002'0ea4       0x4  Data  Lc  xiangzi.o [1]
zong::sentA5            0x2002'0eb4       0x4  Data  Lc  xiangzi.o [1]
zong::textModeStartTime
                        0x2002'0eac       0x4  Data  Lc  xiangzi.o [1]
zong::waitingForFeedback
                        0x2002'0eb0       0x4  Data  Lc  xiangzi.o [1]
zyPID                   0x2000'0b20      0x1c  Data  Gb  zhuangtaiji.o [1]


[1] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\code_17896468042025673432.dir
[2] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_fatfs_6246014242642884545.dir
[3] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_sdmmc_11335736203712880765.dir
[4] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\components_usb_17375887477349322284.dir
[5] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_device_3373403627085545761.dir
[6] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\sdk_drivers_13454894214809048885.dir
[7] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\user_c_11930989646151335152.dir
[8] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_common_1201165462030747823.dir
[9] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_components_14936052685886095442.dir
[10] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_device_6570275422786408770.dir
[11] = C:\Users\<USER>\Desktop\RT1064_Library-master\SeekFree_RT1064_Opensource_Library\project\iar\program\nor_sdram_zf_dtcm\Obj\zf_driver_11922370083829174090.dir
[12] = dl7M_tlf.a
[13] = m7M_tlv.a
[14] = rt7M_tl.a
[15] = shb_l.a
[16] = zf_device_config.a

    1'210 bytes of readonly  code memory
   69'100 bytes of readwrite code memory
   61'744 bytes of readonly  data memory
  198'267 bytes of readwrite data memory

Errors: none
Warnings: none
