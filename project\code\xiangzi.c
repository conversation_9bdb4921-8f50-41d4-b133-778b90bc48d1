#include "zf_common_headfile.h"

// 全局状态变量，控制是否进入推箱子模式
bool inTextMode = false;
int class = 0;
int class1 = 0;
float target_raw = 0.0f;
int yyy;
float recorded_slope = 0.0f;  // 记录检测到箱子时的slope值
float angle_slope = 0.0f;     // 用于phase 3阶段转回的目标角度

// 标记是否已记录识别结果
static bool hasRecordedResult = false;

// 位置稳定时间设置 (ms)
int position_stable_time = 400;     // 位置条件稳定需持续的时间，达到后发送标志位

// 存储从phase 0切换到phase 1时的角度
float phase_transition_angle = 0.0f;

// 时间间隔控制变量
int time1 = 800; // box_detected触发后的冷却时间(ms)
int time2 = 1800;  // side_detected触发后的冷却时间(ms)
int time3 = 1500; // push操作后禁止触发side_detected的冷却时间(ms)
unsigned long last_push_time = 0; // 最后一次执行push的时间戳
unsigned long last_side_detect_time = 0; // 最后一次执行side_detected的时间戳

// 当前记录的个数
int classIndex = 0;
ClassEntry classValues[MAX_CLASS_VALUES];
int anglepia = 0;
int phase = 0;
static bool isInFineTuning = false; // 新增：标记是否处于微调状态
static bool distance_cleared_for_phase1 = false; // 新增：标记是否已经为phase1清零过路程
// 记录识别结果的函数
void record_detection_result(void)
{
    // 如果还没记录，则记录当前的 class 值和 class1 值，并置标志为 true
    if (!hasRecordedResult) {
        if (class == 15) {
            classValues[classIndex].type = 1;    // 显示数字
            classValues[classIndex].value = number;  // 记录 number
        } else {
            classValues[classIndex].type = 0;    // 显示汉字
            classValues[classIndex].value = class;   // 记录 class
        }
        // 无论显示类型如何，都记录当前的推箱子方向
        classValues[classIndex].direction = class1;  // 记录 class1（推箱子方向）
        
        // 记录当前的slope值，用于anglepia判断条件
        recorded_slope = (float)slope;  // 将整型slope转换为浮点型保存
        
        classIndex++;
        hasRecordedResult = true;
    }
}

void push(void)
{
    // 进入 text 模式后锁定状态
    inTextMode = true;
    
    // 调用函数记录识别结果已在zong中完成
    
    static bool first_call = true;
    static bool cleared_ti = false;  // 确保在进入超过200阶段时只清零一次 ti
    static int saved_class1 = 0;     // 保存初始的class1值
    static unsigned long phase_transition_start_time = 0;  // 阶段过渡开始时间，用于平滑过渡
    static bool in_transition = false;  // 是否在过渡阶段
    static unsigned long phase3_align_start_time = 0;  // 用于跟踪phase 3中角度调整的起始时间
    static bool phase3_timeout_active = false;  // 标记phase 3是否已激活超时检测
    float speed[3];

    // 第一次调用时清零累计路程并获取保存的推箱子方向
    if (first_call)
    {
        total_distance = 0.0f;
        // 从最近一条记录中获取推箱子方向，避免使用可能被改变的全局变量
        if (classIndex > 0) {
            saved_class1 = classValues[classIndex - 1].direction; // 使用保存在记录中的方向
        } else {
            // 如果没有记录，则使用当前全局变量
            saved_class1 = class1;
        }
        anglepia = 0; // 初始化角度差
        first_call = false;
        isInFineTuning = false; // 每次text开始时，重置微调状态
        in_transition = false;  // 重置过渡阶段标志
        phase_transition_start_time = 0;  // 重置过渡计时
        phase3_align_start_time = 0;  // 重置phase 3角度调整计时
        phase3_timeout_active = false;  // 重置phase 3超时标志
        
        // 计算angle_slope，考虑recorded_slope的偏移量
        // slope_raw是记录的原始角度，需要根据推箱子方向添加或减去recorded_slope
        // if (saved_class1 == 1) {  // 向左推（绕箱子右侧）
        //     // 如果在Phase 0中减小阈值（需要转更多角度）
        //     // 那么在Phase 3中，应该增加目标角度
        //     angle_slope = slope_raw + recorded_slope;
        //     if (angle_slope > 180.0f)
        //         angle_slope -= 360.0f;
        // } else {  // 向右推（绕箱子左侧）
        //     // 如果在Phase 0中增大阈值（需要转更少角度）
        //     // 那么在Phase 3中，应该减少目标角度
            angle_slope = slope_raw - recorded_slope;
            if (angle_slope < -180.0f)
                angle_slope += 360.0f;
        // }
    }
    
    int target_qh = 0;  // 目标前后距离
    int target_lr = 0;  // 目标左右距离
    // int target_raw = 0; // 目标角度
    float output_qh = PID_Compute(&qhPID[0], target_qh, distance_mm);
    float output_lr = PID_Compute(&zyPID[0], target_lr, pia);
    // float output_slope = PID_Compute(&anglePID[0], 0, slope * 0.05f);
    // float output_raw = PID_Compute(&anglePID[0], target_raw, eulerAngle_yaw);
    
    // 使用保存的saved_class1确定参数，避免受到外部class1变化的影响
    float vx_dir = saved_class1 == 0 ? 1.0f : -1.0f;
    float wz_dir = saved_class1 == 0 ? 1.0f : -1.0f;
    float angflag = saved_class1 == 0 ? 1.0f : -1.0f;
    // // 静态变量用于跟踪当前阶段
    // phase = 0;
    static unsigned long yyy_1_start_time = 0;
    static unsigned long yyy_0_start_time = 0;
    static unsigned long angle_condition_start_time = 0;
    static float distance_at_yyy_1 = 0.0f;
    // 计算角度差，考虑跨越 ±180° 的情况
    anglepia = eulerAngle_yaw - slope_raw;

    // 处理跨越边界情况
    if (anglepia <= -180)
        anglepia += 360;
    else if (anglepia > 180)
        anglepia -= 360;

    // 首先检查是否在过渡阶段
    if (in_transition)
    {
        // 计算过渡进度，范围0.0-1.0
        float transition_progress = (float)(tim - phase_transition_start_time) / 100.0f;
        if (transition_progress > 1.0f) transition_progress = 1.0f;
        
        // 在过渡期间实现平滑变化
        if (phase == 1) {
            // 从phase 1到phase 2的平滑过渡：vy从48平滑变为-48
            vx = 0.0f;
            vy = 48.0f * (1.0f - transition_progress) + (-48.0f) * transition_progress;
            g_target_angular_velocity_setpoint = 0.0f;
            
            // 当过渡完成时立即切换phase
            if (transition_progress >= 1.0f) {
                in_transition = false;  // 过渡结束
                phase = 2;  // 从phase 1过渡到phase 2
            }
        } else if (phase == 2) {
            // 其他phase切换立即执行
            in_transition = false;
            phase = 3;  // 从phase 2过渡到phase 3
        }
    }
    // 不在过渡阶段时，根据当前phase执行对应逻辑
    else if (phase == 0)
    {
        // 根据recorded_slope调整转向角度阈值
        float angle_threshold = 75.0f;
        
        // 根据推箱方向应用slope偏移量
        if (saved_class1 == 0) {  // 向左推（绕箱子右侧）
            // slope大于0表示需要向左转更多角度
            angle_threshold -= recorded_slope;  // 减小阈值，需要转更多角度
        } else {  // 向右推（绕箱子左侧）
            // slope大于0表示需要向左转更多角度，对于向右推的情况需要反向调整
            angle_threshold += recorded_slope;  // 增大阈值，需要转更少角度
        }
        
        // 根据 angflag 值确定角度判断条件，应用调整后的阈值
        bool angle_condition = (angflag > 0) ? (anglepia > angle_threshold) : (anglepia < -angle_threshold);
        // 当角度条件满足时进入下一阶段
        if (angle_condition)
        {
            if (angle_condition_start_time == 0)
            {
                angle_condition_start_time = tim; // 开始计时
            }

            if (tim - angle_condition_start_time < 300)
            {
                output_lr = PID_Compute(&zyPID[0], target_lr, pia);
                // 前300ms执行
                vx = -output_lr*2.2;
                vy = 0;
                g_target_angular_velocity_setpoint = 0;
            }
            else
            {
                // 300ms后
                // 记录当前角度，用于在phase 1和phase 2中保持
                phase_transition_angle = eulerAngle_yaw;
                phase = 1;
                yyy_1_start_time = 0;
                angle_condition_start_time = 0; // 为下次使用重置计时器
                                // 前300ms执行
                // vx = 0;
                // vy = 0;
                // g_target_angular_velocity_setpoint = 0;
            }
        }
        else
        {
            angle_condition_start_time = 0; // 如果条件不满足，重置计时器
            target_qh = 80;  // 目标前后距离
            target_lr = 0;    // 目标左右距离
            output_qh = PID_Compute(&qhPID[0], target_qh, distance_mm);
            output_lr = PID_Compute(&zyPID[0], target_lr, pia);

            // 限幅处理
            // if (fabs(420.0f - distance_mm) < 10.0f) {
            //     output_qh = 0.0f;
            // } else {
            //     if (output_qh > 130) output_qh = 130;
            //     if (output_qh < -45) output_qh = -45;
            //             }
        // 限幅处理
        // if (fabs(420.0f - distance_mm) < 10.0f) {
        //     output_qh = 0.0f;
        // } else {
        //     if (output_qh > 130) output_qh = 130;
        //     if (output_qh < -45) output_qh = -45;
        //             }
        
        // if (fabs(pia) < 8.0f) {
        //     output_lr = 0.0f;
        // }
        
        // 设置速度
        vx=vx_dir*56-output_lr*2; // 将左右距离作为vx
        vy=-output_qh; // 将前后距离作为vy
        g_target_angular_velocity_setpoint = wz_dir * 32.0f;
        }
    }
    else if (phase == 1)
    {
        // 只有第一次进入 phase == 1 时才清零路程
        if (!distance_cleared_for_phase1) {
            total_distance = 0.0f;
            distance_cleared_for_phase1 = true;
        }
        
        // 计算当前角度与记录角度的差值
        float angle_diff = eulerAngle_yaw - phase_transition_angle;
        // 处理跨越边界情况
        if (angle_diff <= -180)
            angle_diff += 360;
        else if (angle_diff > 180)
            angle_diff -= 360;
            
        // 使用PID控制保持角度
        float output_angle = PID_Compute(&anglePID[0], 0, angle_diff);
        
        vx=-output_lr*1.6; // 根据需要调整左右位置
        vy=48.0f;
        g_target_angular_velocity_setpoint=output_angle*0.3; // 使用角度差值进行控制
        
        if (total_distance > 25.0f)
        {
            if (yyy == 1)
            {
                if (yyy_1_start_time == 0)
                {
                    yyy_1_start_time = tim;
                }
                else if (tim - yyy_1_start_time >= 100)
                {
                    distance_at_yyy_1 = total_distance;
                    // 开始进入过渡阶段
                    in_transition = true;
                    phase_transition_start_time = tim;
                    yyy_0_start_time = 0;
                }
            }
            else
            {
                yyy_1_start_time = 0;
            }
        }
    }
    else if (phase == 2)
    {
        // 计算当前角度与记录角度的差值
        float angle_diff = eulerAngle_yaw - phase_transition_angle;
        // 处理跨越边界情况
        if (angle_diff <= -180)
            angle_diff += 360;
        else if (angle_diff > 180)
            angle_diff -= 360;
            
        // 使用PID控制保持角度
        float output_angle = PID_Compute(&anglePID[0], 0, angle_diff);
        
        vx=0.0f;
        vy=-48.0f; // 将前后距离作为vy
        g_target_angular_velocity_setpoint=output_angle; // 使用角度差值进行控制
        bool distance_condition = total_distance >= distance_at_yyy_1 + 6.0f;
        bool yyy_condition = false;
        if (yyy == 0)
        {
            if (yyy_0_start_time == 0)
            {
                yyy_0_start_time = tim;
            }
            else if (tim - yyy_0_start_time >= 70)
            {
                yyy_condition = true;
            }
        }
        else
        {
            yyy_0_start_time = 0;
        }
        if (distance_condition && yyy_condition)
        {
            // 开始进入过渡阶段
            in_transition = true;
            phase_transition_start_time = tim;
        }
    }
    else if (phase == 3)
    {
        // 计算角度差，保证正确处理角度跨越边界的情况
        // 使用angle_slope代替slope_raw，包含了slope偏移量的调整
        float angle_diff = eulerAngle_yaw - angle_slope;
        if (angle_diff <= -180.0f)
            angle_diff += 360.0f;
        else if (angle_diff > 180.0f)
            angle_diff -= 360.0f;
        
        // 初始化超时计时
        if (!phase3_timeout_active) {
            phase3_align_start_time = tim;
            phase3_timeout_active = true;
        }
        
        // 检查是否满足退出条件：角度差小于阈值或已超时
        if (
            fabsf(angle_diff) <= 10.0f || (tim - phase3_align_start_time > 2000)                )
        {
            vx = 0.0f; 
            vy = 0.0f;
            g_target_angular_velocity_setpoint = 0.0f;        
            inTextMode = false;
            // 重置状态以便下次进入 text 模式时可以重新记录
            first_call = true;
            cleared_ti = false;
            phase = 0;
            hasRecordedResult = false;  // 重置记录标志
            isInFineTuning = false; // 确保在流程结束时重置微调状态
            distance_cleared_for_phase1 = false; // 重置路程清零标志
            in_transition = false;  // 重置过渡阶段标志
            phase_transition_start_time = 0;  // 重置过渡计时
            recorded_slope = 0.0f;  // 重置slope偏移量
            angle_slope = 0.0f;  // 重置角度目标值
            phase3_align_start_time = 0;  // 重置角度调整计时
            phase3_timeout_active = false;  // 重置超时标志
            uart_write_byte(UART_2, 0x87);
            
            // 更新最后一次执行push的时间戳 - 仅在退出push模式时记录
            last_push_time = tim;
        }
        else
        {
            // 根据角度差的大小调整旋转速度，使用包含slope偏移量的目标角度
            float rotation_speed = PID_Compute(&boxAlignAnglePID[0], angle_slope, eulerAngle_yaw);
            
            // 根据是否在圆环内决定控制策略
            if (Island_State > 0) {
                // 在圆环内，保持vy为0，只调整角度
                vx = 0.0f;
                vy = 0.0f;
                g_target_angular_velocity_setpoint = rotation_speed * 0.6f;
            } else {
                // 不在圆环内的处理
                // if (fabsf(angle_diff) <= 35.0f) {
                //     // 当角度差小于35度时，加入平滑过渡的控制
                //     // 保持vy为35.0f不变
                //     vy = 35.0f;
                    
                //     // 使用跑赛道时的控制方式进行平滑过渡
                //     float target_heading = 80.0f;
                //     g_target_angular_velocity_setpoint = PID_Compute(&saoxianPIDh[0], target_heading, feedbackposition1) * 0.5f+rotation_speed * 0.6f;
                //     vx = -g_target_angular_velocity_setpoint * 0.75f; // 根据角速度计算vx
                // } else {
                //     // 角度差大于35度时，使用原来的控制方式
                //     vx = 0.0f;
                //     vy = 35.0f;
                //     g_target_angular_velocity_setpoint = rotation_speed * 0.6f;
                // }
            }
        }
    }
    
    // 注意：last_push_time的更新已移到退出push模式的地方
}


int flag_finish = 0;                             // 上位机识别完成标志
static bool isBoxDetectedEntry = true;           // 第一次检测到箱子的标志
float slope_raw = 0.0f;                         // 记录用于对齐的目标角度
float target_qh = 0.0f;                         // 目标前后距离
float target_lr = 0.0f;                         // 目标左右距离
int  yyy = 0;                                   // 箱子Y轴位置标志
int safe = 1;                                   // 安全标志，0为紧急停止
int left = 0;                                   // 左侧传感器，0表示检测到箱子
int right = 0;                                  // 右侧传感器，0表示检测到箱子
static bool hasReceivedFeedback = false;         // 记录是否接收到过上位机反馈

// 侧面传感器检测箱子相关变量
static bool side_detected = false;               // 侧面检测到箱子标志
static unsigned long side_detection_start_time = 0; // 侧面检测开始时间
static int detected_side = -1;                   // 检测到的侧面（0：左侧，1：右侧）

void zong() {
    // 静态变量定义
    static unsigned long positionStableStartTime = 0;  // 位置满足条件的计时起点
    static int positionStable = 0;                    // 位置是否稳定的标志
    static unsigned long textModeStartTime = 0;        // 进入文本模式的计时起点
    static int waitingForFeedback = 0;                // 等待上位机回应标志
    static int sentA5 = 0;                            // 是否发送了A5的标志
    static unsigned long initialPhaseStartTime = 0;    // 初始阶段计时起点
    
    // 计算控制输出
    float output_qh = PID_Compute(&qhPID[0], target_qh, distance_mm);
    float output_lr = PID_Compute(&zyPID[0], target_lr, pia);

        
    // 与上位机通信 - 根据box_detected状态决定发送不同指令
    if (box_detected == 1) {
        // 检测到箱子，发送0xb3指令，告诉上位机开始识别
        uart_write_byte(UART_4, 0xb3);
    } else {
        // 未检测到箱子，发送0x92指令，告诉上位机可以暂停识别
        uart_write_byte(UART_4, 0x92);
    }
    
    // 如果已在推箱子模式中，执行push函数并返回
    if (inTextMode) {
        push();
        return;
    }
    
    // 检查是否在冷却时间内
    bool in_cooldown_period = (tim - last_push_time < time1);
    
    // 仅当不在冷却期间时，才处理box_detected为1的情况
    if (box_detected == 1 && !in_cooldown_period) {
        // 如果之前是侧面检测到箱子，现在摄像头检测到了，重置侧面检测标志
        if (side_detected) {
            side_detected = false;
            side_detection_start_time = 0;
            detected_side = -1;  // 重置检测侧面的记录
        }
        
        // 第一次检测到箱子时的初始化
        if (isBoxDetectedEntry) {
            robot_x = 0.0f;
            isBoxDetectedEntry = false;
            initialPhaseStartTime = tim;  // 初始化阶段开始时间
            sentA5 = 0;  // 重置发送标志
            hasRecordedResult = false; // 重置记录标志
            hasReceivedFeedback = false; // 重置接收反馈标志
        }
        
        // 位置控制逻辑
        target_qh = 80;  // 目标前后距离
        target_lr = 0;   // 目标左右距离

        // 计算控制输出
        output_qh = PID_Compute(&qhPID[0], target_qh, distance_mm);
        output_lr = PID_Compute(&zyPID[0], target_lr, pia);

        
        // 距离控制
        if (fabs(80.0f - distance_mm) < 5.0f) {
            output_qh = 0.0f;
        } else {
            if (output_qh > 130) output_qh = 130;
            if (output_qh < -b_d1) output_qh = -b_d1;
        }
        
        // 左右对齐控制
        if (fabs(pia) < 8.0f) {
            output_lr = 0.0f;
        }
        
        // 设置速度
        vx = -output_lr * 2;  // 将左右距离作为vx
        vy = -output_qh * 1.2;      // 将前后距离作为vy
        g_target_angular_velocity_setpoint = 0.0f;  // 目标角速度

        // 防撞紧急停止逻辑
        if (safe == 0) {
            vx = -output_lr * 2;  // 保持左右修正
            vy = -30;            // 后退
        }
        
        // 位置检测逻辑 - 仅影响标志位发送
        if (fabs(pia) < 5.0f && fabs(target_qh - distance_mm) < 10.0f) {
            if (positionStableStartTime == 0) {
                positionStableStartTime = tim;  // 开始计时
                positionStable = 0;  // 初始设置为不稳定
            } else if (tim - positionStableStartTime >= position_stable_time && !sentA5) {
                // 位置稳定800ms后，标记位置为稳定并发送标志位
                positionStable = 1;
                slope_raw = eulerAngle_yaw;  // 记录当前角度作为目标角度
                
                // 发送识别标志位
                uart_write_byte(UART_2, 0xA5);  
                sentA5 = 1;  
                waitingForFeedback = 1;  // 开始等待上位机回应
            }
        } else {
            // 位置不满足条件，重置计时
            positionStableStartTime = 0;  
            // 但不重置positionStable标志，让控制逻辑继续执行
        }
        
        // 超时机制 - 如果超过2秒还没有发送标志位，强制发送
        if (tim - initialPhaseStartTime >= 2000 && !sentA5) {
            positionStable = 1;
            slope_raw = eulerAngle_yaw;  // 记录当前角度作为目标角度
            uart_write_byte(UART_2, 0xA5);  // 发送标志位
            sentA5 = 1;
            waitingForFeedback = 1;
        }
        
        // 检查上位机回应并记录识别结果
        if (waitingForFeedback == 1 && flag_finish == 1 && !hasReceivedFeedback) {
            // 记录识别结果
            record_detection_result();
            
            // 记录当前的slope值，用于调整anglepia判断条件
            // 已经在record_detection_result函数中完成
            
            // 标记已收到反馈，这个标志不会被重置，直到下一次检测到箱子
            hasReceivedFeedback = true;
            
            // 开始计时准备进入推箱子模式
            textModeStartTime = tim;
        }
        
        // 检查车辆位置是否适合进入推箱子模式，与flag_finish无关，只看是否收到过反馈
        if (hasReceivedFeedback) {
            if (pia < 10 && pia > -10 && distance_mm > 72 && distance_mm < 88) {
                // 只在位置满足条件时继续计时
                if (tim - textModeStartTime >= 0) {
                    // 满足条件200ms后进入推箱子模式
                    inTextMode = true;  // 进入 push() 模式
                    textModeStartTime = 0;
                    waitingForFeedback = 0;  
                    sentA5 = 0;  
                    // hasReceivedFeedback 不重置，会在下次检测到箱子时重置
                    
                    // // 调用push函数开始推箱子流程
                    // push();
                    return;
                }
            } else {
                // 位置不满足条件，重新开始计时
                textModeStartTime = tim;
            }
        }
        
        return;
    }        // 当 box_detected 为 0 时，检查左右传感器是否检测到箱子
    if (box_detected == 0) {
        // 检查是否在time2冷却时间内
        bool side_in_cooldown = (tim - last_side_detect_time < time2);
        // 检查是否在push操作后的time3冷却时间内
        bool push_in_cooldown = (tim - last_push_time < time3);
        
        // 检查侧面传感器是否检测到箱子（left==0 或 right==0 表示检测到箱子）且不在两种冷却期间内
        if ((left == 0 || right == 0) && !side_detected && !side_in_cooldown && !push_in_cooldown) {
            // 首次检测到侧面有箱子，记录时间和检测到的侧面
            side_detected = true;
            side_detection_start_time = tim;
            
            // 记录最初检测到箱子的侧面（0：左侧，1：右侧）
            if (left == 0) {
                detected_side = 0; // 左侧检测到
            } else {
                detected_side = 1; // 右侧检测到
            }
            
            // 确保后面的循迹代码不会执行
            uart_write_byte(UART_2, 0x87);
            isBoxDetectedEntry = true;
        }
        
        // 如果侧面检测到了箱子
        if (side_detected) {
            // 根据最初记录的检测侧面设置转向方向，而不是当前传感器状态
            float turn_direction = (detected_side == 0) ? 8.0f : -8.0f;
            
            // 先判断是否在前300ms内
            if (tim - side_detection_start_time < 300) {
                // 前300ms内，让车辆完全停止，防止前倾
                vx = 0.0f;
                vy = 0.0f;
                g_target_angular_velocity_setpoint = 0.0f;
            } else {
                // 300ms后，开始后退并适当转向，尝试找到箱子
                vx = 0.0f;
                vy = -30.0f;  // 向后退
                g_target_angular_velocity_setpoint = turn_direction;  // 向检测到箱子的反方向转动
            }
            
            // 检查是否已经超过最大时间限制(1200ms)或者摄像头已经检测到箱子
            if ((tim - side_detection_start_time > 1200) || box_detected == 1) {
                // 重置侧面检测标志和时间
                side_detected = false;
                side_detection_start_time = 0;
                detected_side = -1;  // 重置检测侧面的记录
                
                // 更新最后一次侧面检测的时间戳
                last_side_detect_time = tim;
                
                // 重置其他状态变量
                isBoxDetectedEntry = true;
                positionStableStartTime = 0;
                positionStable = 0;
                textModeStartTime = 0;
                waitingForFeedback = 0;
                sentA5 = 0;
                initialPhaseStartTime = 0;
                hasReceivedFeedback = false;
            }
            
            // 如果在侧面检测模式下，不执行普通的循迹控制
            return;
        } 
        else {
                vx=PID_Compute(&angularRatePID[0], 0, mid_x_offset);
                vy=PID_Compute(&distancePID[0], 0, mid_y_offset)*(-1);
                g_target_angular_velocity_setpoint=PID_Compute(&saoxianPID[0], 0, mid_angle_offset);
            // 没有检测到箱子，正常执行默认控制
            uart_write_byte(UART_2, 0x87);
            isBoxDetectedEntry = true;
            positionStableStartTime = 0;
            positionStable = 0;
            textModeStartTime = 0;
            waitingForFeedback = 0;
            sentA5 = 0;
            initialPhaseStartTime = 0;
            hasReceivedFeedback = false;
            
            // 从isr.c中调用时移除此行，改为在PIT_CH3中执行
            //Motor_Control();
        }
    }
}